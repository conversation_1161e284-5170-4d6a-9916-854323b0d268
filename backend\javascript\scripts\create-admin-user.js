// scripts/create-admin-user.js
import mongoose from 'mongoose';
import User from '../models/User.js';
import AdminSettings from '../models/AdminSettings.js';
import { config } from 'dotenv';
import readline from 'readline';

// Load environment variables
config();

const MONGO_URI = process.env.MONGO_URI;

if (!MONGO_URI) {
    console.error('MONGO_URI is not defined in environment variables');
    process.exit(1);
}

// Create readline interface for user input
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

const question = (query) => {
    return new Promise((resolve) => {
        rl.question(query, resolve);
    });
};

/**
 * Script to create the first admin user
 * This script will:
 * 1. Connect to the database
 * 2. Check if an admin user already exists
 * 3. Prompt for admin user details
 * 4. Create the admin user
 * 5. Update admin settings
 */
const createAdminUser = async () => {
    try {
        console.log('Connecting to MongoDB...');
        await mongoose.connect(MONGO_URI);
        console.log('Connected to MongoDB successfully');

        // Check if admin users already exist
        const existingAdmins = await User.find({ isAdmin: true });
        
        if (existingAdmins.length > 0) {
            console.log(`Found ${existingAdmins.length} existing admin user(s):`);
            existingAdmins.forEach((admin, index) => {
                console.log(`${index + 1}. ${admin.email} (${admin.name || 'No name'}) - Verified: ${admin.isVerified}`);
            });
            
            const proceed = await question('\nDo you want to create another admin user? (y/N): ');
            if (proceed.toLowerCase() !== 'y' && proceed.toLowerCase() !== 'yes') {
                console.log('Admin user creation cancelled');
                return;
            }
        }

        console.log('\n=== Creating Admin User ===');
        
        // Get admin user details
        const name = await question('Enter admin name: ');
        const email = await question('Enter admin email: ');
        const password = await question('Enter admin password (min 6 characters): ');

        // Validate input
        if (!email || !password) {
            throw new Error('Email and password are required');
        }
        
        if (password.length < 6) {
            throw new Error('Password must be at least 6 characters long');
        }

        // Check if user with this email already exists
        const existingUser = await User.findOne({ email });
        if (existingUser) {
            if (existingUser.isAdmin) {
                throw new Error('A user with this email already exists and is already an admin');
            } else {
                // Update existing user to be admin
                existingUser.isAdmin = true;
                existingUser.isVerified = true; // Auto-verify admin users
                if (name) existingUser.name = name;
                if (password) existingUser.password = password; // Will be hashed by pre-save hook
                await existingUser.save();
                console.log('✅ Existing user updated to admin successfully!');
            }
        } else {
            // Create new admin user
            const adminUser = new User({
                name,
                email,
                password,
                isAdmin: true,
                isVerified: true // Auto-verify admin users
            });

            await adminUser.save();
            console.log('✅ New admin user created successfully!');
        }

        // Update admin settings if this is the first admin
        const adminSettings = await AdminSettings.getSettings();
        if (!adminSettings.hasInitialAdmin) {
            await AdminSettings.markInitialAdminRegistered(email);
            console.log('✅ Admin settings updated - initial admin registered');
        }

        console.log('\nAdmin user details:');
        console.log(`Name: ${name || 'Not provided'}`);
        console.log(`Email: ${email}`);
        console.log(`Admin: Yes`);
        console.log(`Verified: Yes`);

    } catch (error) {
        console.error('Failed to create admin user:', error.message);
        process.exit(1);
    } finally {
        rl.close();
        await mongoose.connection.close();
        console.log('\nDatabase connection closed');
    }
};

// Run the script
createAdminUser()
    .then(() => {
        console.log('\nAdmin user creation script completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Admin user creation script failed:', error);
        process.exit(1);
    });
