// File Path: /backend/javascript/config/businessPlanUploadConfig.js
import multer from 'multer';

// Use memory storage for business plan uploads to provide file.buffer for FormData
// This is required for sending files to Python services via FormData.append()
const businessPlanStorage = multer.memoryStorage();

const businessPlanUpload = multer({
    storage: businessPlanStorage,
    limits: { fileSize: 10 * 1024 * 1024 }, // 10MB
    fileFilter: (req, file, cb) => {
        const allowedMimeTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ];

        if (allowedMimeTypes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error("File type not allowed. Only PDF, DOC, and DOCX files are accepted."), false);
        }
    }
});

export default businessPlanUpload;
