import React, { useState, useEffect, useCallback } from 'react';
import {
  FiCreditCard,
  FiLoader,
  FiSave,
  FiDollarSign,
  FiUsers,
  FiTrendingUp,
  FiSettings,
  FiCheckCircle,
  FiAlertTriangle,
} from 'react-icons/fi';

// Define the base URL for your API
const API_BASE_URL = import.meta.env.VITE_NODE_BACKEND_URL || 'http://localhost:3001';

const PaymentManagement = () => {
  // State for storing plans, stats, and UI status
  const [plans, setPlans] = useState([]);
  const [planConfig, setPlanConfig] = useState([]);
  const [editingLimits, setEditingLimits] = useState({});
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [activeTab, setActiveTab] = useState('plan-limits');
  const [activePlanTab, setActivePlanTab] = useState('Starter');
  const [activePricingTab, setActivePricingTab] = useState('pricing'); // 'pricing' or 'features'

  // Function to fetch all necessary data from the backend
  const fetchData = useCallback(async () => {
    setLoading(true);
    setError('');
    try {
      const token = localStorage.getItem('authToken');
      if (!token) {
        throw new Error('Authentication token not found.');
      }

      // Fetch actual plan configuration (source of truth)
      const planConfigResponse = await fetch(`${API_BASE_URL}/api/admin/plan-config`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!planConfigResponse.ok) {
        const errData = await planConfigResponse.json();
        throw new Error(errData.error || 'Failed to fetch plan configuration');
      }

      const planConfigData = await planConfigResponse.json();
      setPlanConfig(planConfigData.planConfig || []);

      // Fetch all subscription plans
      const plansResponse = await fetch(`${API_BASE_URL}/api/admin/subscription-plans`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!plansResponse.ok) {
        const errData = await plansResponse.json();
        throw new Error(errData.error || 'Failed to fetch subscription plans');
      }

      const plansData = await plansResponse.json();

      // FIX: De-duplicate plans to prevent rendering the same plan multiple times
      const uniquePlans = Array.from(new Map(plansData.plans.map(plan => [plan.planName, plan])).values());

      // Sort plans by their defined sortOrder
      uniquePlans.sort((a, b) => a.sortOrder - b.sortOrder);

      setPlans(uniquePlans);

      // Fetch subscription statistics
      const statsResponse = await fetch(`${API_BASE_URL}/api/admin/subscription-stats`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData);
      } else {
        console.warn('Could not fetch subscription stats.');
      }

    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch data when the component mounts
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Initialize editing limits when planConfig changes
  useEffect(() => {
    if (planConfig.length > 0) {
      const initialEditingLimits = {};
      planConfig.forEach(plan => {
        initialEditingLimits[plan.planName] = { ...plan.limits };
      });
      setEditingLimits(initialEditingLimits);
    }
  }, [planConfig]);

  // Handler for updating pricing values in state
  const handlePriceChange = (planName, priceType, value) => {
    setPlans(prevPlans =>
      prevPlans.map(plan =>
        plan.planName === planName
          ? {
              ...plan,
              price: {
                ...plan.price,
                [priceType]: value === '' ? 0 : parseFloat(value)
              }
            }
          : plan
      )
    );
  };

  // Handler for updating a plan's text description
  const handleDescriptionChange = (planName, value) => {
    setPlans(prevPlans =>
        prevPlans.map(p =>
            p.planName === planName ? { ...p, description: value } : p
        )
    );
  };

  // Handler for updating a plan's features
  const handleFeaturesChange = (planName, features) => {
    setPlans(prevPlans =>
        prevPlans.map(p =>
            p.planName === planName ? { ...p, features: features } : p
        )
    );
  };

  // Handler for adding a new feature to a plan
  const handleAddFeature = (planName) => {
    setPlans(prevPlans =>
        prevPlans.map(p =>
            p.planName === planName
                ? { ...p, features: [...(p.features || []), ''] }
                : p
        )
    );
  };

  // Handler for removing a feature from a plan
  const handleRemoveFeature = (planName, featureIndex) => {
    setPlans(prevPlans =>
        prevPlans.map(p =>
            p.planName === planName
                ? {
                    ...p,
                    features: p.features.filter((_, index) => index !== featureIndex)
                }
                : p
        )
    );
  };

  // Handler for updating a specific feature in a plan
  const handleFeatureChange = (planName, featureIndex, value) => {
    setPlans(prevPlans =>
        prevPlans.map(p =>
            p.planName === planName
                ? {
                    ...p,
                    features: p.features.map((feature, index) =>
                        index === featureIndex ? value : feature
                    )
                }
                : p
        )
    );
  };

  // Handler for updating plan configuration limits with validation
  const handlePlanLimitChange = (planName, limitType, value) => {
    // Clear any existing errors when user starts typing
    setError('');

    // Validate input
    if (value !== '' && (isNaN(value) || parseInt(value, 10) < 0)) {
      setError(`Invalid value for ${limitType}. Please enter a non-negative number.`);
      return;
    }

    const numericValue = value === '' ? 0 : parseInt(value, 10);

    // Additional validation for reasonable limits
    if (numericValue > 10000) {
      setError(`Limit for ${limitType} seems too high. Please enter a reasonable value (max 10,000).`);
      return;
    }

    setEditingLimits(prev => ({
      ...prev,
      [planName]: {
        ...prev[planName],
        [limitType]: numericValue
      }
    }));
  };

  // Function to save plan configuration changes with enhanced validation
  const savePlanConfiguration = async (planName) => {
    setSaving(true);
    setError('');
    setSuccess('');

    try {
      const token = localStorage.getItem('authToken');
      if (!token) {
        throw new Error('Authentication token not found. Please log in again.');
      }

      const limits = editingLimits[planName];

      if (!limits) {
        throw new Error('No changes to save');
      }

      // Validate all limits before saving
      const requiredFields = ['pdfUpload', 'businessPlan', 'investorPitch', 'businessQA'];
      const missingFields = requiredFields.filter(field => !(field in limits));

      if (missingFields.length > 0) {
        throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
      }

      // Check for negative values
      const negativeFields = requiredFields.filter(field => limits[field] < 0);
      if (negativeFields.length > 0) {
        throw new Error(`Negative values not allowed for: ${negativeFields.join(', ')}`);
      }

      // Check for unreasonably high values
      const highFields = requiredFields.filter(field => limits[field] > 10000);
      if (highFields.length > 0) {
        throw new Error(`Values too high for: ${highFields.join(', ')}. Maximum allowed is 10,000.`);
      }

      const response = await fetch(`${API_BASE_URL}/api/admin/plan-config`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          planName,
          limits
        }),
      });

      if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.error || `Failed to update ${planName} plan configuration`);
      }

      const result = await response.json();

      // Update the planConfig state with the new values
      setPlanConfig(prev =>
        prev.map(plan =>
          plan.planName === planName
            ? { ...plan, limits: result.config.limits }
            : plan
        )
      );

      setSuccess(`✅ ${planName} plan limits updated successfully! Changes are now live and will be reflected immediately for all users.`);
      setTimeout(() => setSuccess(''), 7000);

      // Optionally refresh the data to ensure consistency
      setTimeout(() => {
        fetchData();
      }, 1000);

    } catch (err) {
      console.error('Save plan configuration error:', err);
      setError(`❌ ${err.message}`);
    } finally {
      setSaving(false);
    }
  };

  // Function to reset plan configuration to original values
  const resetPlanConfiguration = (planName) => {
    const originalPlan = planConfig.find(plan => plan.planName === planName);
    if (originalPlan) {
      setEditingLimits(prev => ({
        ...prev,
        [planName]: { ...originalPlan.limits }
      }));
      setError('');
      setSuccess(`${planName} plan limits reset to original values.`);
      setTimeout(() => setSuccess(''), 3000);
    }
  };

  // Helper function to check if a value has been changed
  const hasValueChanged = (planName, limitType) => {
    const originalPlan = planConfig.find(plan => plan.planName === planName);
    const editingValue = editingLimits[planName]?.[limitType];
    const originalValue = originalPlan?.limits[limitType];
    return editingValue !== originalValue;
  };

  // Helper function to check if any values have been changed for a plan
  const hasAnyChanges = (planName) => {
    const originalPlan = planConfig.find(plan => plan.planName === planName);
    const editingValues = editingLimits[planName];

    if (!originalPlan || !editingValues) return false;

    return Object.keys(editingValues).some(key =>
      editingValues[key] !== originalPlan.limits[key]
    );
  };


  // Function to save a specific plan's updated details
  const savePlan = async (planToSave) => {
    setSaving(true);
    setError('');
    setSuccess('');
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch(`${API_BASE_URL}/api/admin/subscription-plans/${planToSave.planName}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(planToSave),
      });

      if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.error || 'Failed to save plan');
      }

      setSuccess(`${planToSave.displayName} plan saved successfully!`);
      setTimeout(() => setSuccess(''), 4000);
    } catch (err) {
      setError(err.message);
    } finally {
      setSaving(false);
    }
  };



  // Display a loading spinner while fetching initial data
  if (loading) {
    return (
      <div className="p-8 flex items-center justify-center text-slate-400">
        <FiLoader className="w-8 h-8 text-purple-500 animate-spin mr-3" />
        Loading Subscription Data...
      </div>
    );
  }

  // Define tabs for navigation
  const tabs = [
    { id: 'plan-limits', label: 'Plan Limits Management', icon: FiSettings },
    { id: 'pricing', label: 'Pricing & Description', icon: FiDollarSign },
    { id: 'stats', label: 'Statistics', icon: FiTrendingUp },
  ];

  return (
    <div className="h-full flex flex-col bg-slate-800 text-white">
      {/* Header Section */}
      <div className="flex-shrink-0 p-6 border-b border-slate-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <FiCreditCard className="w-7 h-7 text-purple-400 mr-3" />
            <h2 className="text-xl font-bold text-white">Payment & Subscription Management</h2>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="space-y-6">

          {/* Global Error/Success Messages */}
          {error && (
            <div className="mb-4 p-3 bg-red-900/50 border border-red-700 rounded-lg text-red-300 flex items-center">
              <FiAlertTriangle className="w-5 h-5 mr-3" />
              <strong>Error:</strong> {error}
            </div>
          )}
          {success && (
            <div className="mb-4 p-3 bg-green-900/50 border border-green-700 rounded-lg text-green-300 flex items-center">
              <FiCheckCircle className="w-5 h-5 mr-3" />
              {success}
            </div>
          )}

          {/* Tab Navigation */}
          <div className="flex space-x-1 mb-6 bg-slate-900/50 p-1 rounded-lg w-full md:w-auto">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex-1 md:flex-none flex items-center justify-center px-4 py-2 rounded-md transition-all duration-200 text-sm font-semibold ${
                    activeTab === tab.id
                      ? 'bg-purple-600 text-white shadow-md'
                      : 'text-slate-300 hover:text-white hover:bg-slate-700/50'
                  }`}
                >
                  <Icon className="w-4 h-4 mr-2" />
                  {tab.label}
                </button>
              );
            })}
          </div>

          {/* Tab Content: Plan Limits Management */}
          {activeTab === 'plan-limits' && (
            <div className="space-y-8">
              {/* Header */}
              <div className="text-center">
                <h2 className="text-2xl font-bold text-white mb-2">Plan Limits Management</h2>
                <p className="text-slate-400">Configure the actual runtime limits for each plan (from planConfig.js)</p>
              </div>

              {/* Plan Tabs */}
              <div className="flex space-x-2 mb-8 bg-slate-900/50 p-1.5 rounded-xl w-full md:w-auto mx-auto max-w-md">
                {planConfig.filter(plan => plan.planName === 'Starter' || plan.planName === 'Pro').map((plan) => (
                  <button
                    key={plan.planName}
                    onClick={() => setActivePlanTab(plan.planName)}
                    className={`flex-1 flex items-center justify-center px-6 py-3 rounded-lg transition-all duration-300 text-sm font-semibold ${
                      activePlanTab === plan.planName
                        ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg transform scale-105'
                        : 'text-slate-300 hover:text-white hover:bg-slate-700/50'
                    }`}
                  >
                    <span className={`w-3 h-3 rounded-full mr-3 ${
                      plan.planName === 'Starter' ? 'bg-blue-400' : 'bg-purple-400'
                    }`}></span>
                    {plan.displayName}
                  </button>
                ))}
              </div>

              {/* Plan Content */}
              {planConfig.filter(plan => plan.planName === activePlanTab).map((plan) => (
                <div key={plan.planName} className="bg-gradient-to-br from-slate-800/80 to-slate-700/80 rounded-2xl p-8 border border-slate-600/50 shadow-2xl backdrop-blur-sm">
                  <div className="flex flex-wrap items-center justify-between mb-8">
                    <div className="flex items-center">
                      <div className={`w-16 h-16 rounded-2xl flex items-center justify-center mr-4 ${
                        plan.planName === 'Starter'
                          ? 'bg-gradient-to-br from-blue-500/20 to-blue-600/20 border border-blue-400/30'
                          : 'bg-gradient-to-br from-purple-500/20 to-purple-600/20 border border-purple-400/30'
                      }`}>
                        <span className="text-2xl">
                          {plan.planName === 'Starter' ? '🚀' : '⭐'}
                        </span>
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold text-white flex items-center">
                          {plan.displayName}
                          <span className={`ml-4 px-4 py-2 rounded-full text-sm font-bold ${
                            plan.planName === 'Starter'
                              ? 'bg-gradient-to-r from-blue-500/20 to-blue-600/20 text-blue-300 border border-blue-400/50'
                              : 'bg-gradient-to-r from-purple-500/20 to-purple-600/20 text-purple-300 border border-purple-400/50'
                          }`}>
                            {plan.planName}
                          </span>
                        </h3>
                        <p className="text-slate-400 mt-1">Runtime limits from planConfig.js</p>
                      </div>
                    </div>
                    <div className="flex space-x-3 mt-4 md:mt-0">
                      <button
                        onClick={() => {
                          if (window.confirm(`Are you sure you want to update the ${plan.planName} plan limits? This will immediately affect all users on this plan.`)) {
                            savePlanConfiguration(plan.planName);
                          }
                        }}
                        disabled={saving || !hasAnyChanges(plan.planName)}
                        className={`flex items-center px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-200 shadow-lg hover:shadow-xl ${
                          hasAnyChanges(plan.planName) && !saving
                            ? 'bg-green-600 hover:bg-green-700 text-white animate-pulse'
                            : 'bg-slate-600 text-slate-300 cursor-not-allowed opacity-50'
                        }`}
                      >
                        <FiSave className="w-4 h-4 mr-2" />
                        {saving ? 'Saving...' : hasAnyChanges(plan.planName) ? 'Save Changes' : 'No Changes'}
                      </button>
                      <button
                        onClick={() => {
                          if (window.confirm(`Reset ${plan.planName} plan limits to original values?`)) {
                            resetPlanConfiguration(plan.planName);
                          }
                        }}
                        disabled={saving}
                        className="flex items-center px-6 py-3 bg-slate-600 hover:bg-slate-500 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg text-sm font-semibold transition-colors"
                      >
                        <span className="w-4 h-4 mr-2">↺</span>
                        Reset
                      </button>
                    </div>
                  </div>

                  {/* Tool Limits Grid */}
                  <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                    {/* PDF Upload Limit */}
                    <div className="bg-gradient-to-br from-red-500/10 to-red-600/10 p-6 rounded-xl border border-red-500/20 hover:border-red-400/40 transition-all duration-300">
                      <div className="flex items-center mb-4">
                        <div className="w-12 h-12 bg-gradient-to-br from-red-500/20 to-red-600/20 rounded-xl flex items-center justify-center mr-4">
                          <span className="text-red-400 text-xl">📄</span>
                        </div>
                        <div>
                          <h4 className="text-lg font-bold text-white">PDF Upload</h4>
                          <p className="text-sm text-slate-400">Monthly limit</p>
                        </div>
                      </div>
                      <div className="text-center">
                        <input
                          type="number"
                          value={editingLimits[plan.planName]?.pdfUpload || 0}
                          onChange={(e) => handlePlanLimitChange(plan.planName, 'pdfUpload', e.target.value)}
                          className={`w-full text-3xl font-bold text-red-400 bg-transparent border-2 rounded-lg px-3 py-2 text-center focus:outline-none mb-2 transition-all duration-200 ${
                            hasValueChanged(plan.planName, 'pdfUpload')
                              ? 'border-yellow-400 shadow-lg shadow-yellow-400/20 focus:border-yellow-300'
                              : 'border-red-500/30 focus:border-red-400'
                          }`}
                          min="0"
                        />
                        <div className="text-sm text-slate-400">uploads per month</div>
                      </div>
                    </div>

                    {/* Business Plan Limit */}
                    <div className="bg-gradient-to-br from-blue-500/10 to-blue-600/10 p-6 rounded-xl border border-blue-500/20 hover:border-blue-400/40 transition-all duration-300">
                      <div className="flex items-center mb-4">
                        <div className="w-12 h-12 bg-gradient-to-br from-blue-500/20 to-blue-600/20 rounded-xl flex items-center justify-center mr-4">
                          <span className="text-blue-400 text-xl">📊</span>
                        </div>
                        <div>
                          <h4 className="text-lg font-bold text-white">Business Plan</h4>
                          <p className="text-sm text-slate-400">Monthly limit</p>
                        </div>
                      </div>
                      <div className="text-center">
                        <input
                          type="number"
                          value={editingLimits[plan.planName]?.businessPlan || 0}
                          onChange={(e) => handlePlanLimitChange(plan.planName, 'businessPlan', e.target.value)}
                          className={`w-full text-3xl font-bold text-blue-400 bg-transparent border-2 rounded-lg px-3 py-2 text-center focus:outline-none mb-2 transition-all duration-200 ${
                            hasValueChanged(plan.planName, 'businessPlan')
                              ? 'border-yellow-400 shadow-lg shadow-yellow-400/20 focus:border-yellow-300'
                              : 'border-blue-500/30 focus:border-blue-400'
                          }`}
                          min="0"
                        />
                        <div className="text-sm text-slate-400">plans per month</div>
                      </div>
                    </div>

                    {/* Investor Pitch Limit */}
                    <div className="bg-gradient-to-br from-green-500/10 to-green-600/10 p-6 rounded-xl border border-green-500/20 hover:border-green-400/40 transition-all duration-300">
                      <div className="flex items-center mb-4">
                        <div className="w-12 h-12 bg-gradient-to-br from-green-500/20 to-green-600/20 rounded-xl flex items-center justify-center mr-4">
                          <span className="text-green-400 text-xl">🎯</span>
                        </div>
                        <div>
                          <h4 className="text-lg font-bold text-white">Pitch</h4>
                          <p className="text-sm text-slate-400">Monthly limit</p>
                        </div>
                      </div>
                      <div className="text-center">
                        <input
                          type="number"
                          value={editingLimits[plan.planName]?.investorPitch || 0}
                          onChange={(e) => handlePlanLimitChange(plan.planName, 'investorPitch', e.target.value)}
                          className={`w-full text-3xl font-bold text-green-400 bg-transparent border-2 rounded-lg px-3 py-2 text-center focus:outline-none mb-2 transition-all duration-200 ${
                            hasValueChanged(plan.planName, 'investorPitch')
                              ? 'border-yellow-400 shadow-lg shadow-yellow-400/20 focus:border-yellow-300'
                              : 'border-green-500/30 focus:border-green-400'
                          }`}
                          min="0"
                        />
                        <div className="text-sm text-slate-400">pitches per month</div>
                      </div>
                    </div>

                    {/* Question Business Limit */}
                    <div className="bg-gradient-to-br from-yellow-500/10 to-yellow-600/10 p-6 rounded-xl border border-yellow-500/20 hover:border-yellow-400/40 transition-all duration-300">
                      <div className="flex items-center mb-4">
                        <div className="w-12 h-12 bg-gradient-to-br from-yellow-500/20 to-yellow-600/20 rounded-xl flex items-center justify-center mr-4">
                          <span className="text-yellow-400 text-xl">❓</span>
                        </div>
                        <div>
                          <h4 className="text-lg font-bold text-white">Question Business</h4>
                          <p className="text-sm text-slate-400">Daily limit</p>
                        </div>
                      </div>
                      <div className="text-center">
                        <input
                          type="number"
                          value={editingLimits[plan.planName]?.businessQA || 0}
                          onChange={(e) => handlePlanLimitChange(plan.planName, 'businessQA', e.target.value)}
                          className={`w-full text-3xl font-bold text-yellow-400 bg-transparent border-2 rounded-lg px-3 py-2 text-center focus:outline-none mb-2 transition-all duration-200 ${
                            hasValueChanged(plan.planName, 'businessQA')
                              ? 'border-orange-400 shadow-lg shadow-orange-400/20 focus:border-orange-300'
                              : 'border-yellow-500/30 focus:border-yellow-400'
                          }`}
                          min="0"
                        />
                        <div className="text-sm text-slate-400">questions per day</div>
                      </div>
                    </div>
                  </div>

                  {/* Plan Summary */}
                  <div className="mt-8 p-6 bg-gradient-to-r from-slate-900/60 to-slate-800/60 rounded-xl border border-slate-600/50 backdrop-blur-sm">
                    <h4 className="text-lg font-bold text-white mb-4 flex items-center">
                      <span className="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                      Plan Configuration Summary
                    </h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                      <div className="text-center p-4 bg-slate-800/50 rounded-lg">
                        <div className="text-slate-400 text-sm mb-1">PDF Upload</div>
                        <div className="text-white font-bold text-lg">
                          {editingLimits[plan.planName]?.pdfUpload || plan.limits.pdfUpload}
                        </div>
                        <div className="text-xs text-slate-500">per month</div>
                      </div>
                      <div className="text-center p-4 bg-slate-800/50 rounded-lg">
                        <div className="text-slate-400 text-sm mb-1">Business Plan</div>
                        <div className="text-white font-bold text-lg">
                          {editingLimits[plan.planName]?.businessPlan || plan.limits.businessPlan}
                        </div>
                        <div className="text-xs text-slate-500">per month</div>
                      </div>
                      <div className="text-center p-4 bg-slate-800/50 rounded-lg">
                        <div className="text-slate-400 text-sm mb-1">Pitch</div>
                        <div className="text-white font-bold text-lg">
                          {editingLimits[plan.planName]?.investorPitch || plan.limits.investorPitch}
                        </div>
                        <div className="text-xs text-slate-500">per month</div>
                      </div>
                      <div className="text-center p-4 bg-slate-800/50 rounded-lg">
                        <div className="text-slate-400 text-sm mb-1">Question Business</div>
                        <div className="text-white font-bold text-lg">
                          {editingLimits[plan.planName]?.businessQA || plan.limits.businessQA}
                        </div>
                        <div className="text-xs text-slate-500">per day</div>
                      </div>
                    </div>
                    <div className="mt-4 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                      <p className="text-blue-300 text-sm flex items-center">
                        <span className="w-4 h-4 mr-2">ℹ️</span>
                        These values are read from <code className="bg-slate-800 px-2 py-1 rounded text-xs">planConfig.js</code> and represent the actual runtime limits enforced by the system.
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}



          {/* Tab Content: Pricing */}
          {activeTab === 'pricing' && (
            <div className="space-y-8">
                {plans.map((plan) => (
                    <div key={plan.planName} className="group relative overflow-hidden">
                        {/* Enhanced Plan Card */}
                        <div className="bg-gradient-to-br from-slate-800/90 to-slate-900/90 rounded-2xl border border-slate-600/50 shadow-2xl backdrop-blur-sm hover:border-slate-500/70 transition-all duration-300">
                            {/* Plan Header with Icon and Badge */}
                            <div className="relative p-8 pb-6">
                                {/* Background Pattern */}
                                <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                                <div className="relative flex items-center justify-between mb-6">
                                    <div className="flex items-center space-x-4">
                                        {/* Plan Icon */}
                                        <div className={`w-16 h-16 rounded-2xl flex items-center justify-center shadow-lg ${
                                            plan.planName === 'Starter'
                                                ? 'bg-gradient-to-br from-blue-500/20 to-blue-600/20 border border-blue-400/30'
                                                : 'bg-gradient-to-br from-purple-500/20 to-purple-600/20 border border-purple-400/30'
                                        }`}>
                                            <FiCreditCard className={`w-8 h-8 ${
                                                plan.planName === 'Starter' ? 'text-blue-400' : 'text-purple-400'
                                            }`} />
                                        </div>

                                        {/* Plan Title and Badge */}
                                        <div>
                                            <h3 className="text-2xl font-bold text-white mb-2">{plan.displayName}</h3>
                                            <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold ${
                                                plan.planName === 'Starter'
                                                    ? 'bg-blue-500/20 text-blue-300 border border-blue-400/50'
                                                    : 'bg-purple-500/20 text-purple-300 border border-purple-400/50'
                                            }`}>
                                                <FiSettings className="w-3 h-3 mr-1" />
                                                {plan.planName} Plan
                                            </div>
                                        </div>
                                    </div>

                                    {/* Save Button */}
                                    <button
                                        onClick={() => savePlan(plan)}
                                        disabled={saving}
                                        className="group/btn relative flex items-center px-6 py-3 bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 disabled:from-slate-600 disabled:to-slate-700 text-white rounded-xl font-semibold transition-all duration-300 shadow-lg shadow-emerald-500/25 hover:shadow-emerald-500/40 disabled:shadow-none transform hover:scale-105 disabled:scale-100"
                                    >
                                        <FiSave className="w-5 h-5 mr-2 transition-transform group-hover/btn:rotate-12" />
                                        <span>{saving ? 'Saving...' : 'Save Changes'}</span>
                                        {saving && <FiLoader className="w-4 h-4 ml-2 animate-spin" />}
                                    </button>
                                </div>
                            </div>

                            {/* Pricing Sub-Tabs */}
                            <div className="px-8 pb-4">
                                <div className="flex space-x-1 bg-slate-800/50 rounded-xl p-1">
                                    <button
                                        onClick={() => setActivePricingTab('pricing')}
                                        className={`flex-1 flex items-center justify-center px-4 py-3 rounded-lg font-semibold transition-all duration-300 ${
                                            activePricingTab === 'pricing'
                                                ? 'bg-gradient-to-r from-emerald-500 to-emerald-600 text-white shadow-lg'
                                                : 'text-slate-400 hover:text-white hover:bg-slate-700/50'
                                        }`}
                                    >
                                        <FiDollarSign className="w-4 h-4 mr-2" />
                                        Pricing
                                    </button>
                                    <button
                                        onClick={() => setActivePricingTab('features')}
                                        className={`flex-1 flex items-center justify-center px-4 py-3 rounded-lg font-semibold transition-all duration-300 ${
                                            activePricingTab === 'features'
                                                ? 'bg-gradient-to-r from-sky-500 to-sky-600 text-white shadow-lg'
                                                : 'text-slate-400 hover:text-white hover:bg-slate-700/50'
                                        }`}
                                    >
                                        <FiCheckCircle className="w-4 h-4 mr-2" />
                                        Features
                                    </button>
                                </div>
                            </div>

                            {/* Tab Content */}
                            <div className="px-8 pb-8">
                                {/* Pricing Tab Content */}
                                {activePricingTab === 'pricing' && (
                                    <div className="space-y-6">
                                        {/* Price Inputs Grid */}
                                        <div className="grid gap-6 md:grid-cols-2">
                                            {/* Monthly Price */}
                                            <div className="space-y-3">
                                                <label className="flex items-center text-sm font-semibold text-slate-200 mb-2">
                                                    <FiDollarSign className="w-4 h-4 mr-2 text-emerald-400" />
                                                    Monthly Price
                                                </label>
                                                <div className="relative">
                                                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                                        <span className="text-slate-400 text-lg font-semibold">$</span>
                                                    </div>
                                                    <input
                                                        type="number"
                                                        step="0.01"
                                                        value={plan.price.monthly}
                                                        onChange={(e) => handlePriceChange(plan.planName, 'monthly', e.target.value)}
                                                        className="w-full pl-8 pr-4 py-4 bg-slate-800/80 border border-slate-600/50 rounded-xl text-white text-lg font-semibold placeholder-slate-400 focus:ring-2 focus:ring-emerald-500/50 focus:border-emerald-500/50 transition-all duration-300 hover:border-slate-500/70"
                                                        placeholder="0.00"
                                                    />
                                                </div>
                                                <p className="text-xs text-slate-400 flex items-center">
                                                    <FiTrendingUp className="w-3 h-3 mr-1" />
                                                    Billed monthly
                                                </p>
                                            </div>

                                            {/* Yearly Price */}
                                            <div className="space-y-3">
                                                <label className="flex items-center text-sm font-semibold text-slate-200 mb-2">
                                                    <FiDollarSign className="w-4 h-4 mr-2 text-emerald-400" />
                                                    Yearly Price
                                                </label>
                                                <div className="relative">
                                                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                                        <span className="text-slate-400 text-lg font-semibold">$</span>
                                                    </div>
                                                    <input
                                                        type="number"
                                                        step="0.01"
                                                        value={plan.price.yearly}
                                                        onChange={(e) => handlePriceChange(plan.planName, 'yearly', e.target.value)}
                                                        className="w-full pl-8 pr-4 py-4 bg-slate-800/80 border border-slate-600/50 rounded-xl text-white text-lg font-semibold placeholder-slate-400 focus:ring-2 focus:ring-emerald-500/50 focus:border-emerald-500/50 transition-all duration-300 hover:border-slate-500/70"
                                                        placeholder="0.00"
                                                    />
                                                </div>
                                                <p className="text-xs text-slate-400 flex items-center">
                                                    <FiCheckCircle className="w-3 h-3 mr-1" />
                                                    Billed annually
                                                </p>
                                            </div>
                                        </div>

                                        {/* Description Section */}
                                        <div className="space-y-3">
                                            <label className="flex items-center text-sm font-semibold text-slate-200">
                                                <FiUsers className="w-4 h-4 mr-2 text-sky-400" />
                                                Plan Description
                                            </label>
                                            <div className="relative">
                                                <textarea
                                                    value={plan.description}
                                                    onChange={(e) => handleDescriptionChange(plan.planName, e.target.value)}
                                                    className="w-full px-4 py-4 bg-slate-800/80 border border-slate-600/50 rounded-xl text-white placeholder-slate-400 focus:ring-2 focus:ring-sky-500/50 focus:border-sky-500/50 transition-all duration-300 hover:border-slate-500/70 resize-none"
                                                    rows="4"
                                                    placeholder="Enter a compelling description that highlights the key benefits and features of this plan..."
                                                />
                                                <div className="absolute bottom-3 right-3 text-xs text-slate-500">
                                                    {plan.description?.length || 0}/200
                                                </div>
                                            </div>
                                            <p className="text-xs text-slate-400 flex items-center">
                                                <FiAlertTriangle className="w-3 h-3 mr-1" />
                                                This description will be displayed on the public pricing page
                                            </p>
                                        </div>
                                    </div>
                                )}

                                {/* Features Tab Content */}
                                {activePricingTab === 'features' && (
                                    <div className="space-y-6">
                                        <div className="flex items-center justify-between">
                                            <div>
                                                <h4 className="text-lg font-semibold text-white">Plan Features</h4>
                                                <p className="text-sm text-slate-400">Manage the feature list displayed on pricing cards</p>
                                            </div>
                                            <button
                                                onClick={() => handleAddFeature(plan.planName)}
                                                className="flex items-center px-4 py-2 bg-gradient-to-r from-sky-500 to-sky-600 hover:from-sky-600 hover:to-sky-700 text-white rounded-lg font-semibold transition-all duration-300 shadow-lg shadow-sky-500/25 hover:shadow-sky-500/40"
                                            >
                                                <FiCheckCircle className="w-4 h-4 mr-2" />
                                                Add Feature
                                            </button>
                                        </div>

                                        <div className="space-y-3">
                                            {(plan.features || []).map((feature, index) => (
                                                <div key={index} className="flex items-center space-x-3">
                                                    <div className="flex-1">
                                                        <input
                                                            type="text"
                                                            value={feature}
                                                            onChange={(e) => handleFeatureChange(plan.planName, index, e.target.value)}
                                                            className="w-full px-4 py-3 bg-slate-800/80 border border-slate-600/50 rounded-xl text-white placeholder-slate-400 focus:ring-2 focus:ring-sky-500/50 focus:border-sky-500/50 transition-all duration-300 hover:border-slate-500/70"
                                                            placeholder="Enter feature description..."
                                                        />
                                                    </div>
                                                    <button
                                                        onClick={() => handleRemoveFeature(plan.planName, index)}
                                                        className="flex items-center justify-center w-10 h-10 bg-red-500/20 hover:bg-red-500/30 text-red-400 hover:text-red-300 rounded-lg transition-all duration-300"
                                                    >
                                                        <FiAlertTriangle className="w-4 h-4" />
                                                    </button>
                                                </div>
                                            ))}

                                            {(!plan.features || plan.features.length === 0) && (
                                                <div className="text-center py-8 text-slate-400">
                                                    <FiCheckCircle className="w-12 h-12 mx-auto mb-3 opacity-50" />
                                                    <p>No features added yet. Click "Add Feature" to get started.</p>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                ))}
            </div>
          )}

          {/* Tab Content: Statistics */}
          {activeTab === 'stats' && stats && (
            <div className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                <div className="bg-slate-700/50 rounded-lg p-6 border border-slate-600"><div className="flex items-center"><FiUsers className="w-8 h-8 text-blue-400 mr-4" /><div><div className="text-3xl font-bold text-white">{stats.totalUsers}</div><div className="text-sm text-slate-400">Total Users</div></div></div></div>
                <div className="bg-slate-700/50 rounded-lg p-6 border border-slate-600"><div className="flex items-center"><FiCheckCircle className="w-8 h-8 text-green-400 mr-4" /><div><div className="text-3xl font-bold text-white">{stats.activeUsers}</div><div className="text-sm text-slate-400">Verified Users</div></div></div></div>
                <div className="bg-slate-700/50 rounded-lg p-6 border border-slate-600"><div className="flex items-center"><FiDollarSign className="w-8 h-8 text-green-400 mr-4" /><div><div className="text-3xl font-bold text-white">${stats.estimatedMonthlyRevenue.toFixed(2)}</div><div className="text-sm text-slate-400">Est. Monthly Revenue</div></div></div></div>
                <div className="bg-slate-700/50 rounded-lg p-6 border border-slate-600"><div className="flex items-center"><FiTrendingUp className="w-8 h-8 text-purple-400 mr-4" /><div><div className="text-3xl font-bold text-white">{stats.planDistribution.length}</div><div className="text-sm text-slate-400">Active Plans</div></div></div></div>
              </div>
              <div className="bg-slate-700/50 rounded-lg p-6 border border-slate-600">
                <h3 className="text-lg font-semibold text-white mb-4">Plan Distribution</h3>
                <div className="space-y-4">
                  {stats.planDistribution.map((planStat) => (
                    <div key={planStat._id || 'Unknown'} className="flex items-center justify-between p-2 rounded-md hover:bg-slate-700">
                      <div className="flex items-center"><div className={`w-3 h-3 rounded-full mr-4 ${planStat._id === 'Starter' ? 'bg-blue-500' : planStat._id === 'Pro' ? 'bg-purple-500' : 'bg-yellow-500'}`}></div><span className="text-white font-medium">{planStat._id || 'Not Subscribed'}</span></div>
                      <div className="text-right"><div className="text-white font-medium">{planStat.count} Users</div><div className="text-sm text-slate-400">{planStat.verified} verified</div></div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PaymentManagement;