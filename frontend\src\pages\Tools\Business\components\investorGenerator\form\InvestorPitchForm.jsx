import React, { useState, useCallback, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
    FiArrowRight, FiLoader, FiCheckCircle, FiCircle,
    FiThumbsUp, FiAlertTriangle, FiUpload, FiFile, FiX, FiInfo
} from 'react-icons/fi';
import { updateUserSubscription, selectAuthToken } from '../../../../../../store/features/auth/authSlice';

// Import the component that knows how to parse and display the tagged text
import FormattedPitchDisplay from '../textFormate/FormattedPitchDisplay';

// Import RTL detection utility
import { isRTL } from '../../../../../../utils/textUtils';

//==================================================================
//  1. Constants and Language Configuration
//==================================================================

const LANGUAGES = Object.freeze([
    { name: 'English', code: 'English', flag: '🇺🇸', native: 'English', rtl: false },
    { name: 'Español', code: 'Spanish', flag: '🇪🇸', native: 'Español', rtl: false },
    { name: 'Français', code: 'French', flag: '🇫🇷', native: 'Français', rtl: false },
    { name: 'Deutsch', code: 'German', flag: '🇩🇪', native: 'Deutsch', rtl: false },
    { name: '日本語', code: 'Japanese', flag: '🇯🇵', native: '日本語', rtl: false },
    { name: '中文', code: 'Chinese', flag: '🇨🇳', native: '中文简体', rtl: false },
    { name: 'العربية', code: 'Arabic', flag: '🇸🇦', native: 'العربية', rtl: true },
    { name: 'עברית', code: 'Hebrew', flag: '🇮🇱', native: 'עברית', rtl: true },
    { name: 'Русский', code: 'Russian', flag: '🇷🇺', native: 'Русский', rtl: false },
    { name: 'Português', code: 'Portuguese', flag: '🇵🇹', native: 'Português', rtl: false },
    { name: 'Italiano', code: 'Italian', flag: '🇮🇹', native: 'Italiano', rtl: false },
    { name: 'हिन्दी', code: 'Hindi', flag: '🇮🇳', native: 'हिन्दी', rtl: false },
]);

const ACCEPTED_FILE_TYPES = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
];

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

//==================================================================
//  2. Local Storage Utilities
//==================================================================

const STORAGE_KEY = 'investor_pitch_form_data';
const STORAGE_EXPIRY_HOURS = 1;

const saveFormDataToStorage = (formData, businessPlanFile) => {
    try {
        const dataToStore = {
            formData,
            businessPlanFileName: businessPlanFile?.name || null,
            timestamp: Date.now(),
            expiryTime: Date.now() + (STORAGE_EXPIRY_HOURS * 60 * 60 * 1000)
        };
        localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToStore));
    } catch (error) {
        console.warn('Failed to save form data to localStorage:', error);
    }
};

const loadFormDataFromStorage = () => {
    try {
        const stored = localStorage.getItem(STORAGE_KEY);
        if (!stored) return null;

        const data = JSON.parse(stored);

        // Check if data has expired
        if (Date.now() > data.expiryTime) {
            localStorage.removeItem(STORAGE_KEY);
            return null;
        }

        return data;
    } catch (error) {
        console.warn('Failed to load form data from localStorage:', error);
        localStorage.removeItem(STORAGE_KEY);
        return null;
    }
};

const clearFormDataFromStorage = () => {
    try {
        localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
        console.warn('Failed to clear form data from localStorage:', error);
    }
};

//==================================================================
//  3. Child Components (Self-contained within this file)
//==================================================================

/**
 * Tooltip component for enhanced user guidance
 */
const Tooltip = React.memo(({ children, content }) => {
    const [isVisible, setIsVisible] = useState(false);

    return (
        <div className="relative inline-block">
            <div
                onMouseEnter={() => setIsVisible(true)}
                onMouseLeave={() => setIsVisible(false)}
                className="cursor-help"
            >
                {children}
            </div>
            {isVisible && (
                <div className="absolute z-50 w-64 p-3 text-xs text-slate-200 bg-slate-800 border border-slate-600 rounded-lg shadow-xl -top-2 left-full ml-2">
                    <div className="absolute top-3 -left-1 w-2 h-2 bg-slate-800 border-l border-b border-slate-600 transform rotate-45"></div>
                    {content}
                </div>
            )}
        </div>
    );
});

/**
 * Language selector component with RTL/LTR support
 */
const LanguageSelector = React.memo(({ selectedLanguage, onLanguageChange, helpText }) => {
    const [isOpen, setIsOpen] = useState(false);
    const selectedLang = LANGUAGES.find(lang => lang.code === selectedLanguage) || LANGUAGES[0];

    return (
        <div className="flex flex-col">
            <div className="flex items-center mb-2">
                <label className="block text-sm font-medium text-slate-300">
                    Language <span className="text-slate-500 text-xs">(Optional)</span>
                </label>
                <Tooltip content="Select the language for your investor pitch. The AI will generate content in your chosen language with proper text direction (RTL for Arabic/Hebrew). This ensures your pitch is culturally appropriate and professionally formatted for your target investors.">
                    <FiInfo className="w-4 h-4 text-slate-400 ml-2" />
                </Tooltip>
            </div>
            <div className="relative">
                <button
                    type="button"
                    onClick={() => setIsOpen(!isOpen)}
                    className="w-full bg-slate-900/70 border border-slate-700 rounded-lg py-2.5 px-4 text-slate-200 focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500 outline-none transition-all duration-300 flex items-center justify-between"
                >
                    <div className="flex items-center space-x-2">
                        <span className="text-lg">{selectedLang.flag}</span>
                        <span>{selectedLang.native}</span>
                    </div>
                    <FiArrowRight className={`w-4 h-4 transition-transform duration-200 ${isOpen ? 'rotate-90' : ''}`} />
                </button>

                {isOpen && (
                    <div className="absolute z-50 w-full mt-1 bg-slate-800 border border-slate-600 rounded-lg shadow-xl max-h-60 overflow-y-auto">
                        {LANGUAGES.map((lang) => (
                            <button
                                key={lang.code}
                                type="button"
                                onClick={() => {
                                    onLanguageChange(lang.code);
                                    setIsOpen(false);
                                }}
                                className={`w-full px-4 py-3 text-left hover:bg-slate-700 transition-colors duration-200 flex items-center space-x-3 ${
                                    selectedLanguage === lang.code ? 'bg-yellow-500/10 text-yellow-400' : 'text-slate-200'
                                }`}
                            >
                                <span className="text-lg">{lang.flag}</span>
                                <span>{lang.native}</span>
                                {lang.rtl && <span className="text-xs text-slate-500 ml-auto">RTL</span>}
                            </button>
                        ))}
                    </div>
                )}
            </div>
            {helpText && <p className="mt-1.5 text-xs text-slate-500">{helpText}</p>}
        </div>
    );
});

/**
 * A memoized, reusable input field component.
 */
const FormInput = React.memo(({ id, label, type = "text", placeholder, value, onChange, required = true, helpText, hasUploadedFile = false }) => {
    const isRequired = required && !hasUploadedFile;
    return (
        <div className="flex flex-col">
            <label htmlFor={id} className="block text-sm font-medium text-slate-300 mb-2">
                {label}
                {!isRequired && <span className="text-slate-500 text-xs">(Optional)</span>}
                {hasUploadedFile && <span className="text-green-400 text-xs">(Auto-filled from document)</span>}
            </label>
            <input
                type={type}
                id={id}
                name={id}
                value={value}
                onChange={onChange}
                placeholder={placeholder}
                required={isRequired}
                className="w-full bg-slate-900/70 border border-slate-700 rounded-lg py-2.5 px-4 text-slate-200 placeholder-slate-500 focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500 outline-none transition-all duration-300"
            />
            {helpText && <p className="mt-1.5 text-xs text-slate-500">{helpText}</p>}
        </div>
    );
});

/**
 * A memoized, reusable textarea component for longer text inputs.
 */
const FormTextarea = React.memo(({ id, label, placeholder, value, onChange, rows = 3, helpText, required = true, hasUploadedFile = false }) => {
    const isRequired = required && !hasUploadedFile;
    return (
        <div className="flex flex-col">
            <label htmlFor={id} className="block text-sm font-medium text-slate-300 mb-2">
                {label}
                {!isRequired && <span className="text-slate-500 text-xs">(Optional)</span>}
                {hasUploadedFile && <span className="text-green-400 text-xs">(Auto-filled from document)</span>}
            </label>
            <textarea
                id={id}
                name={id}
                value={value}
                onChange={onChange}
                placeholder={placeholder}
                required={isRequired}
                rows={rows}
                className="w-full bg-slate-900/70 border border-slate-700 rounded-lg py-2.5 px-4 text-slate-200 placeholder-slate-500 focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500 outline-none transition-all duration-300 resize-y"
            />
            {helpText && <p className="mt-1.5 text-xs text-slate-500">{helpText}</p>}
        </div>
    );
});

/**
 * Business plan file upload component
 */
const BusinessPlanUpload = React.memo(({ selectedFile, onFileSelect, onFileRemove, error }) => {
    const fileInputRef = useRef(null);

    const handleFileSelect = (e) => {
        const file = e.target.files[0];
        if (file) {
            // Validate file type
            if (!ACCEPTED_FILE_TYPES.includes(file.type)) {
                onFileSelect(null, 'Please upload a PDF, DOC, or DOCX file.');
                return;
            }

            // Validate file size
            if (file.size > MAX_FILE_SIZE) {
                onFileSelect(null, 'File size must be less than 10MB.');
                return;
            }

            onFileSelect(file, null);
        }
    };

    const formatFileSize = (bytes) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    return (
        <div className="flex flex-col">
            <div className="flex items-center mb-2">
                <label className="block text-sm font-medium text-slate-300">
                    Business Plan Document <span className="text-slate-500 text-xs">(Optional)</span>
                </label>
                <Tooltip content="Upload your existing business plan (PDF, DOC, or DOCX) to help the AI generate more accurate and personalized investor pitch content. The AI will analyze your document and extract relevant information such as financial projections, market analysis, and strategic insights to enhance the pitch quality. This feature significantly improves the relevance and accuracy of the generated content.">
                    <FiInfo className="w-4 h-4 text-slate-400 ml-2" />
                </Tooltip>
            </div>

            <div className="relative">
                <input
                    ref={fileInputRef}
                    type="file"
                    accept=".pdf,.doc,.docx"
                    onChange={handleFileSelect}
                    className="hidden"
                />

                {!selectedFile ? (
                    <button
                        type="button"
                        onClick={() => fileInputRef.current?.click()}
                        className="w-full bg-slate-900/70 border-2 border-dashed border-slate-600 rounded-lg py-6 px-4 text-slate-300 hover:border-yellow-500/50 hover:bg-slate-800/50 transition-all duration-300 flex flex-col items-center space-y-2"
                    >
                        <FiUpload className="w-8 h-8 text-slate-400" />
                        <div className="text-center">
                            <p className="font-medium">Upload Business Plan</p>
                            <p className="text-sm text-slate-500">PDF, DOC, or DOCX (max 10MB)</p>
                        </div>
                    </button>
                ) : (
                    <div className="bg-slate-900/70 border border-slate-700 rounded-lg p-4 flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                            <FiFile className="w-5 h-5 text-yellow-400" />
                            <div>
                                <p className="text-slate-200 font-medium">{selectedFile.name}</p>
                                <p className="text-slate-500 text-sm">{formatFileSize(selectedFile.size)}</p>
                            </div>
                        </div>
                        <button
                            type="button"
                            onClick={onFileRemove}
                            className="text-slate-400 hover:text-red-400 transition-colors duration-200"
                        >
                            <FiX className="w-5 h-5" />
                        </button>
                    </div>
                )}
            </div>

            {error && (
                <p className="mt-1.5 text-xs text-red-400">{error}</p>
            )}

            <div className="mt-2 flex items-start space-x-2 text-xs text-slate-500">
                <FiInfo className="w-3 h-3 mt-0.5 flex-shrink-0" />
                <p>
                    Upload your existing business plan to help the AI generate more accurate and personalized investor pitch content.
                    The AI will extract relevant information from your document to enhance the pitch quality.
                </p>
            </div>
        </div>
    );
});

/**
 * A component to display animated steps while the AI is generating the pitch.
 */
const GeneratingPitchSteps = () => {
    const steps = [
        "Analyzing Your Vision...",
        "Crafting the Core Narrative...",
        "Injecting Persuasive Language...",
        "Finalizing Your Pitch..."
    ];
    const [currentStep, setCurrentStep] = useState(0);

    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentStep(prev => (prev < steps.length - 1 ? prev + 1 : prev));
        }, 1800);
        return () => clearInterval(timer);
    }, [steps.length]);

    return (
        <div className="w-full bg-slate-900/50 border border-slate-700 rounded-2xl p-8 flex flex-col items-center text-center animate-fade-in">
            <h3 className="text-xl font-bold text-yellow-400 mb-6">Your Pitch is Being Generated...</h3>
            <div className="space-y-4 w-full max-w-sm">
                {steps.map((step, index) => {
                    const isCompleted = index < currentStep;
                    const isCurrent = index === currentStep;
                    return (
                        <div key={index} className="flex items-center text-left text-slate-300 transition-opacity duration-500" style={{ opacity: isCompleted || isCurrent ? 1 : 0.5 }}>
                            {isCompleted ? <FiCheckCircle className="w-6 h-6 mr-4 text-green-400 flex-shrink-0" /> : isCurrent ? <FiLoader className="w-6 h-6 mr-4 text-yellow-400 animate-spin flex-shrink-0" /> : <FiCircle className="w-6 h-6 mr-4 text-slate-500 flex-shrink-0" />}
                            <span className={isCurrent ? "font-semibold" : ""}>{step}</span>
                        </div>
                    );
                })}
            </div>
            <p className="text-slate-500 mt-8 text-sm">This may take a moment. The AI is crafting the perfect words.</p>
        </div>
    );
};


//==================================================================
//  2. The Main Form Component
//==================================================================
const InvestorPitchForm = () => {
    const dispatch = useDispatch();
    const token = useSelector(selectAuthToken);

    // State for form fields
    const [formData, setFormData] = useState({
        projectName: '', industry: '', projectDescription: '', problemStatement: '', solution: '',
        targetAudience: '', competition: '', pitchObjective: '', fundingAmount: '',
        growthPlan: '', toneOfVoice: 'Persuasive', language: 'English',
    });

    // State for file upload
    const [businessPlanFile, setBusinessPlanFile] = useState(null);
    const [fileError, setFileError] = useState(null);

    // State for managing API interaction (idle, loading, success, error)
    const [apiState, setApiState] = useState('idle');
    const [error, setError] = useState(null);
    const [generatedPitch, setGeneratedPitch] = useState('');

    // Memoized change handler for form inputs to prevent unnecessary re-renders
    const handleChange = useCallback((e) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
    }, []);

    // Language change handler
    const handleLanguageChange = useCallback((languageCode) => {
        setFormData(prev => ({ ...prev, language: languageCode }));
    }, []);

    // File upload handlers
    const handleFileSelect = useCallback((file, error) => {
        setBusinessPlanFile(file);
        setFileError(error);
    }, []);

    const handleFileRemove = useCallback(() => {
        setBusinessPlanFile(null);
        setFileError(null);
    }, []);

    // Load form data from localStorage on component mount
    useEffect(() => {
        const storedData = loadFormDataFromStorage();
        if (storedData) {
            setFormData(storedData.formData);
            // Note: We can't restore the actual file, but we can show the filename
            if (storedData.businessPlanFileName) {
                console.log(`Previously uploaded file: ${storedData.businessPlanFileName}`);
            }
        }
    }, []);

    // Save form data to localStorage whenever it changes
    useEffect(() => {
        const timeoutId = setTimeout(() => {
            saveFormDataToStorage(formData, businessPlanFile);
        }, 1000); // Debounce saves by 1 second

        return () => clearTimeout(timeoutId);
    }, [formData, businessPlanFile]);

    // Function to handle form submission
    const handleSubmit = async (e) => {
        e.preventDefault();
        setError(null);
        setApiState('loading');

        try {
            // Create FormData for file upload
            const submitData = new FormData();

            // Add form fields
            Object.keys(formData).forEach(key => {
                submitData.append(key, formData[key]);
            });

            // Add business plan file if selected
            if (businessPlanFile) {
                submitData.append('businessPlanFile', businessPlanFile);
            }

            // Use JavaScript backend for investor pitch generation (consistent with other business tools)
            const response = await fetch('/api/investor-pitch/generate', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    // Don't set Content-Type header - let browser set it with boundary for FormData
                },
                body: submitData,
            });
            const data = await response.json();
            if (!response.ok) {
                throw new Error(data.error || `HTTP error! status: ${response.status}`);
            }

            // Update user subscription in Redux if returned from backend
            if (data.subscription) {
                dispatch(updateUserSubscription(data.subscription));
            }

            setGeneratedPitch(data.generatedPitch);
            setApiState('success');
        } catch (err) {
            setError(err.message);
            setApiState('error');
            console.error('API Error:', err);
        }
    };

    // Function to reset the form and state to start over
    const handleReset = () => {
        setApiState('idle');
        setGeneratedPitch('');
        setError(null);
        setBusinessPlanFile(null);
        setFileError(null);
        clearFormDataFromStorage();
        // Reset form data to initial state
        setFormData({
            projectName: '', industry: '', projectDescription: '', problemStatement: '', solution: '',
            targetAudience: '', competition: '', pitchObjective: '', fundingAmount: '',
            growthPlan: '', toneOfVoice: 'Persuasive', language: 'English',
        });
    };

    // Conditional Rendering: Show loading animation
    if (apiState === 'loading') {
        return <GeneratingPitchSteps />;
    }
    
    // Conditional Rendering: Show the formatted result
    if (apiState === 'success') {
        return <FormattedPitchDisplay pitchText={generatedPitch} onReset={handleReset} />;
    }

    // Default Render: Show the form for 'idle' or 'error' states
    return (
        <form onSubmit={handleSubmit} className="w-full space-y-8 animate-fade-in">
            {/* Enhanced Features Section */}
            <div className="bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-green-500/10 border border-slate-600 rounded-2xl p-6">
                <div className="flex items-center mb-4">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse mr-3"></div>
                    <h3 className="text-lg font-semibold text-slate-200">Enhanced AI Features</h3>
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                    {/* Language Selection */}
                    <div className="bg-slate-900/30 border border-slate-700 rounded-xl p-4">
                        <LanguageSelector
                            selectedLanguage={formData.language}
                            onLanguageChange={handleLanguageChange}
                            helpText="Choose the language for your investor pitch. The AI will generate content in your selected language with proper text direction."
                        />
                    </div>

                    {/* Business Plan Upload */}
                    <div className="bg-slate-900/30 border border-slate-700 rounded-xl p-4">
                        <BusinessPlanUpload
                            selectedFile={businessPlanFile}
                            onFileSelect={handleFileSelect}
                            onFileRemove={handleFileRemove}
                            error={fileError}
                        />
                    </div>
                </div>
            </div>

            {/* Core Information Section */}
            <div className="bg-slate-900/30 border border-slate-700 rounded-2xl p-6">
                <div className="flex items-center mb-6">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse mr-3"></div>
                    <h3 className="text-lg font-semibold text-slate-200">Core Business Information</h3>
                </div>

                {businessPlanFile && (
                    <div className="mb-6 bg-green-500/10 border border-green-500/30 rounded-lg p-4">
                        <div className="flex items-center space-x-2 text-green-400 text-sm">
                            <FiCheckCircle className="w-4 h-4" />
                            <span className="font-medium">Business plan uploaded!</span>
                        </div>
                        <p className="text-slate-300 text-sm mt-2">
                            All form fields are now optional. The AI will extract information from your uploaded document
                            and use it to generate a comprehensive investor pitch. You can still fill in or modify any fields
                            to provide additional context or override the extracted information.
                        </p>
                    </div>
                )}

                <div className="grid md:grid-cols-2 gap-6">
                    <FormInput
                        id="projectName"
                        label="Project / Company Name"
                        placeholder="e.g., Innovatech Solutions"
                        value={formData.projectName}
                        onChange={handleChange}
                        hasUploadedFile={!!businessPlanFile}
                    />
                    <FormInput
                        id="industry"
                        label="Industry / Field"
                        placeholder="e.g., SaaS, FinTech, E-commerce"
                        value={formData.industry}
                        onChange={handleChange}
                        hasUploadedFile={!!businessPlanFile}
                    />
                </div>
                <FormTextarea
                    id="projectDescription"
                    label="Brief Project Description"
                    placeholder="What is the core idea of your venture?"
                    value={formData.projectDescription}
                    onChange={handleChange}
                    helpText="Summarize your venture in 1-2 powerful sentences."
                    hasUploadedFile={!!businessPlanFile}
                />
                <FormTextarea
                    id="problemStatement"
                    label="The Problem"
                    placeholder="What specific pain point are you solving?"
                    value={formData.problemStatement}
                    onChange={handleChange}
                    hasUploadedFile={!!businessPlanFile}
                />
                <FormTextarea
                    id="solution"
                    label="Your Solution"
                    placeholder="Describe your product or service and how it elegantly solves the problem."
                    value={formData.solution}
                    onChange={handleChange}
                    hasUploadedFile={!!businessPlanFile}
                />
                <FormTextarea
                    id="targetAudience"
                    label="Target Audience / Customers"
                    placeholder="e.g., Small business owners, university students"
                    value={formData.targetAudience}
                    onChange={handleChange}
                    hasUploadedFile={!!businessPlanFile}
                />
                <FormTextarea
                    id="competition"
                    label="Competitive Advantage"
                    placeholder="Who are your main competitors and what makes you superior?"
                    value={formData.competition}
                    onChange={handleChange}
                    helpText="What is your unique selling proposition (USP)?"
                    hasUploadedFile={!!businessPlanFile}
                />
                <FormTextarea
                    id="growthPlan"
                    label="Future Vision / Growth Plan"
                    placeholder="Outline your plans for the next 1-3 years."
                    value={formData.growthPlan}
                    onChange={handleChange}
                    rows={4}
                    hasUploadedFile={!!businessPlanFile}
                />

                <div className="grid md:grid-cols-2 gap-6">
                    <FormInput
                        id="pitchObjective"
                        label="Pitch Objective"
                        placeholder="e.g., Secure Seed Funding, Find a Partner"
                        value={formData.pitchObjective}
                        onChange={handleChange}
                        helpText="What is the primary goal of this pitch?"
                        hasUploadedFile={!!businessPlanFile}
                    />
                    <FormInput
                        id="fundingAmount"
                        label="Funding Amount Requested"
                        type="text"
                        placeholder="e.g., $50,000"
                        value={formData.fundingAmount}
                        onChange={handleChange}
                        required={false}
                        hasUploadedFile={!!businessPlanFile}
                    />
                </div>

                <div>
                    <label htmlFor="toneOfVoice" className="block text-sm font-medium text-slate-300 mb-2">Desired Tone of Voice</label>
                    <select id="toneOfVoice" name="toneOfVoice" value={formData.toneOfVoice} onChange={handleChange} className="w-full bg-slate-900/70 border border-slate-700 rounded-lg py-2.5 px-4 text-slate-200 focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500 outline-none transition-all duration-300 appearance-none bg-no-repeat" style={{ backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%239ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e")`, backgroundPosition: 'right 0.75rem center', backgroundSize: '1.25em 1.25em' }}>
                        <option>Persuasive</option><option>Formal</option><option>Friendly</option><option>Motivational</option><option>Professional</option>
                    </select>
                </div>
            </div>

            {/* Display error message if the API call fails */}
            {apiState === 'error' && error && (
                <div className="bg-red-500/10 border border-red-500/30 text-red-300 p-4 rounded-lg flex items-center text-sm">
                    <FiAlertTriangle className="w-5 h-5 mr-3 flex-shrink-0" />
                    <div><strong>Generation Failed:</strong> {error}</div>
                </div>
            )}

            <div className="pt-4 flex justify-end">
                <button type="submit" className="group flex items-center justify-center w-full sm:w-auto px-6 py-3 bg-yellow-500 hover:bg-yellow-600 text-slate-900 font-bold rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105">
                    Generate Pitch <FiArrowRight className="ml-2 w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
                </button>
            </div>
        </form>
    );
};

export default InvestorPitchForm;