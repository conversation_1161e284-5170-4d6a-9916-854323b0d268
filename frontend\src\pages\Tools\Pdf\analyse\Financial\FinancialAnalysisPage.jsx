// src/pages/Tools/Pdf/analyse/Financial/FinancialAnalysisPage.jsx
import React, { useState, useCallback, useMemo, Suspense, lazy } from 'react';
import axios from 'axios';
import { FiLoader, FiDollarSign } from 'react-icons/fi';
import { useAuth } from '../../../../../context/AuthContext';

// Lazy Load Components
const FileUploadDropzone = lazy(() => import('../../../../../components/Tools/Pdf/chat/upload/FileUploadDropzone'));
const FinancialResultsDisplay = lazy(() => import('./components/FinancialResultsDisplay'));

// Import reusable components
import UploadLimit from '../../../../../common/messages/UploadLimit';
import ErrorMessages from '../../../../../common/messages/ErrorMessages';

// Constants
const MAX_FILE_SIZE_MB = 25; // 25MB max file size
const NODEJS_FINANCIAL_ANALYSIS_URL = 'http://localhost:3001/api/financial/analyze';

const FinancialAnalysisPage = ({ openAuthPromptModal }) => {
  const { isAuthenticated, isLoading: authIsLoading, currentUser, incrementUploadCount, token } = useAuth();

  const [pdfFile, setPdfFile] = useState(null);
  const [isProcessingAPICall, setIsProcessingAPICall] = useState(false);
  const [uploadError, setUploadError] = useState('');
  const [apiErrors, setApiErrors] = useState([]);
  const [dragActive, setDragActive] = useState(false);
  const [analysisResults, setAnalysisResults] = useState(null);
  const inputRef = React.useRef(null);

  // Check if user is allowed to upload based on subscription
  const isAllowedToUpload = useMemo(() => {
    if (!currentUser || !currentUser.subscription) return false;
    
    const { planName, freeTierUploadCount, proTierUploadCount } = currentUser.subscription;
    
    if (planName === 'Starter') {
      return freeTierUploadCount < 5; // Free tier limit
    } else if (planName === 'Pro') {
      return proTierUploadCount < 25; // Pro tier limit
    }
    
    return false;
  }, [currentUser]);

  // File validation
  const handleFileValidation = useCallback((file) => {
    setUploadError('');
    
    if (!file) {
      setUploadError('No file selected.');
      return false;
    }
    
    if (file.type !== 'application/pdf') {
      setUploadError('Only PDF files are allowed.');
      return false;
    }
    
    if (file.size > MAX_FILE_SIZE_MB * 1024 * 1024) {
      setUploadError(`File size exceeds ${MAX_FILE_SIZE_MB}MB limit.`);
      return false;
    }
    
    return true;
  }, []);

  // Handle file selection
  const handleFiles = useCallback((rawFiles) => {
    if (!isAuthenticated) { 
      if (typeof openAuthPromptModal === 'function') openAuthPromptModal(); 
      return; 
    }
    
    if (!isAllowedToUpload || isProcessingAPICall) return;
    
    setApiErrors([]);
    
    if (!rawFiles || rawFiles.length === 0) return;
    
    const file = rawFiles[0];
    
    if (handleFileValidation(file)) { 
      setPdfFile(file); 
    } else { 
      setPdfFile(null); 
    }
  }, [isAuthenticated, isAllowedToUpload, isProcessingAPICall, handleFileValidation, openAuthPromptModal]);

  // Handle drag events
  const handleDrag = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  // Handle drop event
  const handleDrop = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  }, [handleFiles]);

  // Handle input change
  const handleInputChange = useCallback((e) => {
    e.preventDefault();
    
    if (e.target.files && e.target.files[0]) {
      handleFiles(e.target.files);
    }
  }, [handleFiles]);

  // Handle dropzone click
  const handleDropzoneAreaClick = useCallback(() => {
    if (inputRef.current && !pdfFile) {
      inputRef.current.click();
    }
  }, [pdfFile]);

  // Remove selected file
  const removeFile = useCallback(() => {
    setPdfFile(null);
    setUploadError('');
    if (inputRef.current) {
      inputRef.current.value = '';
    }
  }, []);

  // Process PDF for financial analysis
  const processPdf = useCallback(async () => {
    if (!pdfFile || !isAuthenticated || !isAllowedToUpload) return;
    
    setIsProcessingAPICall(true);
    setApiErrors([]);
    
    const formData = new FormData();
    formData.append('pdfFile', pdfFile);
    
    try {
      const response = await axios.post(NODEJS_FINANCIAL_ANALYSIS_URL, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.data) {
        setAnalysisResults(response.data);
        incrementUploadCount();
      }
    } catch (error) {
      console.error('Error processing PDF:', error);
      
      const errorMessage = error.response?.data?.error || 
                          error.response?.data?.message || 
                          error.message || 
                          'An unexpected error occurred while processing your PDF.';
      
      setApiErrors(prev => [...prev, errorMessage]);
    } finally {
      setIsProcessingAPICall(false);
    }
  }, [pdfFile, isAuthenticated, isAllowedToUpload, token, incrementUploadCount]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-white mb-2">Financial Document Analysis</h1>
          <p className="text-gray-300">Upload a financial PDF document to extract key financial data and insights</p>
        </div>

      {apiErrors.length > 0 && (
        <ErrorMessages errors={apiErrors} onDismiss={() => setApiErrors([])} />
      )}

      {!isAllowedToUpload && isAuthenticated && !authIsLoading && (
        <UploadLimit 
          currentUser={currentUser} 
          toolName="Financial Analysis" 
        />
      )}

      <div className="bg-white rounded-xl shadow-lg overflow-hidden mb-8">
        <div className="p-6">
          <Suspense fallback={<div className="flex justify-center p-8"><FiLoader className="animate-spin text-blue-500 w-8 h-8" /></div>}>
            <div className="mb-6">
              <FileUploadDropzone
                inputRef={inputRef}
                dragActive={dragActive}
                isProcessing={isProcessingAPICall}
                isConfirming={false}
                maxFileSizeMB={MAX_FILE_SIZE_MB}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
                onFileChange={handleInputChange}
                onDropzoneAreaClick={handleDropzoneAreaClick}
                selectedFile={pdfFile}
                onRemoveFile={removeFile}
                uploadError={uploadError}
                disabled={!isAllowedToUpload}
              />
            </div>
          </Suspense>

          {pdfFile && !uploadError && (
            <div className="flex justify-center mt-4">
              <button
                onClick={processPdf}
                disabled={isProcessingAPICall}
                className={`
                  flex items-center justify-center px-6 py-3 rounded-lg font-medium
                  ${isProcessingAPICall 
                    ? 'bg-gray-400 cursor-not-allowed' 
                    : 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-200'}
                `}
              >
                {isProcessingAPICall ? (
                  <>
                    <FiLoader className="animate-spin mr-2" />
                    Processing...
                  </>
                ) : (
                  <>
                    <FiDollarSign className="mr-2" />
                    Analyze Financial Data
                  </>
                )}
              </button>
            </div>
          )}
        </div>
      </div>

      {analysisResults && (
        <Suspense fallback={<div className="flex justify-center p-8"><FiLoader className="animate-spin text-blue-500 w-8 h-8" /></div>}>
          <FinancialResultsDisplay results={analysisResults} />
        </Suspense>
      )}
      </div>
    </div>
  );
};

export default FinancialAnalysisPage;
