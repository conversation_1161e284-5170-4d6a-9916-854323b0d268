import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../../context/AuthContext';

// Component Imports
import PricingCard from '../../../components/common/price/PricingCard';
import PageBackground from '../../../components/common/price/PageBackground';
import PaymentModal from '../../../components/common/price/PaymentModal';

// API Base URL
const API_BASE_URL = import.meta.env.VITE_NODE_BACKEND_URL || 'http://localhost:3001';

// --- Inner component for rendering the pricing cards and header ---
// This component remains unchanged as its logic was correct.
const PricingView = ({
    pricingTiers,
    onPlanSelect,
    isViewLoading,
    pageMessage,
    currentPlanName,
    isCardActionLoading
}) => {
    if (isViewLoading) {
        return (
            <div className="min-h-[60vh] flex flex-col items-center justify-center text-center p-4">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-sky-400 mb-4"></div>
                <p className="text-lg text-sky-300">Loading available plans...</p>
            </div>
        );
    }
     if ((!pricingTiers || pricingTiers.length === 0) && !(pageMessage?.text && pageMessage.type === 'error')) {
         return (
            <div className="min-h-[60vh] flex flex-col items-center justify-center text-center p-4">
                <h2 className="text-2xl font-semibold text-slate-200 mb-2">No Plans Available</h2>
                <p className="text-slate-400 max-w-md">
                    No pricing plans are currently configured. Please check back later.
                </p>
            </div>
        );
    }
    return (
        <div className="relative z-10 flex flex-col items-center justify-center min-h-screen p-4 sm:p-6 lg:p-8">
            <div className="text-center mb-12 mt-10 md:mt-16">
                <h1 className="text-4xl sm:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 mb-4">
                    Choose Your Plan
                </h1>
                <p className="text-lg text-slate-300 max-w-2xl mx-auto">
                    Unlock powerful features and take your productivity to the next level.
                </p>
            </div>
            {pageMessage && pageMessage.text && (
                <p className={`text-center text-sm my-4 p-3 rounded-md border w-full max-w-lg mx-auto ${
                    pageMessage.type === 'error' ? 'bg-red-500/10 border-red-500/30 text-red-300' :
                    pageMessage.type === 'success' ? 'bg-green-500/10 border-green-500/30 text-green-300' :
                    'bg-sky-500/10 border-sky-500/30 text-sky-300'
                }`}>
                    {pageMessage.text}
                </p>
            )}
            {pricingTiers && pricingTiers.length > 0 && (
                <div className="flex justify-center items-center w-full mb-12">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-10 xl:gap-12 max-w-4xl w-full mx-auto px-4">
                        {/* Filter to ensure only Starter and Pro plans are shown */}
                        {pricingTiers
                            .filter(tier => tier.planName === 'Starter' || tier.planName === 'Pro')
                            // Ensure we only have one of each plan type and sort (Starter first, Pro second)
                            .filter((tier, index, self) =>
                                index === self.findIndex(t => t.planName === tier.planName)
                            )
                            .sort((a, b) => {
                                // Sort Starter first, Pro second
                                if (a.planName === 'Starter') return -1;
                                if (b.planName === 'Starter') return 1;
                                return 0;
                            })
                            .map((tier) => (
                                <div key={tier.id || tier.tierName} className="h-full w-full">
                                    <PricingCard
                                        {...tier}
                                        onButtonClick={() => onPlanSelect(tier)}
                                        disabled={isCardActionLoading || (currentPlanName === tier.tierName && tier.actionType === 'purchase')}
                                        isCurrentPlan={currentPlanName === tier.tierName}
                                    />
                                </div>
                            ))
                        }
                    </div>
                </div>
            )}
        </div>
    );
};
PricingView.displayName = 'PricingView';


// --- Main Exported Component: PricingPage (Content part) ---
const PricingPage = () => {
    // Removed unused isPageLoading state
    const [isCardActionLoading, setIsCardActionLoading] = useState(false);
    const [isLoadingPlans, setIsLoadingPlans] = useState(false);
    const [pricingTiers, setPricingTiers] = useState([]);
    const [pageMessage, setPageMessage] = useState({ text: '', type: '' });
    
    // State specifically for the payment modal
    const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
    const [selectedPlanForModal, setSelectedPlanForModal] = useState(null);

    const auth = useAuth();
    const navigate = useNavigate();

    // Function to fetch pricing plans from API
    const fetchPricingPlans = useCallback(async () => {
        setIsLoadingPlans(true);
        setPageMessage({ text: '', type: '' });

        try {
            const response = await fetch(`${API_BASE_URL}/api/admin/public/subscription-plans`);

            if (!response.ok) {
                throw new Error('Failed to fetch pricing plans');
            }

            const data = await response.json();

            if (data.success && data.plans && data.plans.length > 0) {
                // Filter and deduplicate plans to ensure only Starter and Pro are shown
                const filteredPlans = data.plans
                    .filter(plan => plan.planName === 'Starter' || plan.planName === 'Pro')
                    .filter((plan, index, self) =>
                        index === self.findIndex(p => p.planName === plan.planName)
                    )
                    .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

                if (filteredPlans.length > 0) {
                    setPricingTiers(filteredPlans);
                } else {
                    setPageMessage({ text: 'No pricing plans are currently available.', type: 'info' });
                    setPricingTiers([]);
                }
            } else {
                setPageMessage({ text: 'No pricing plans are currently available.', type: 'info' });
                setPricingTiers([]);
            }
        } catch (error) {
            console.error('Error fetching pricing plans:', error);
            setPageMessage({
                text: 'Unable to load pricing plans. Please try again later.',
                type: 'error'
            });
            setPricingTiers([]);
        } finally {
            setIsLoadingPlans(false);
        }
    }, []);

    // Effect to load pricing plans on component mount
    useEffect(() => {
        fetchPricingPlans();
    }, [fetchPricingPlans]);

    // Auto-refresh pricing data every 5 minutes to catch admin updates
    useEffect(() => {
        const interval = setInterval(() => {
            fetchPricingPlans();
        }, 5 * 60 * 1000); // 5 minutes

        return () => clearInterval(interval);
    }, [fetchPricingPlans]);

    // Listen for focus events to refresh data when user returns to tab
    useEffect(() => {
        const handleFocus = () => {
            fetchPricingPlans();
        };

        window.addEventListener('focus', handleFocus);
        return () => window.removeEventListener('focus', handleFocus);
    }, [fetchPricingPlans]);

    // **FIXED**: The handler for selecting a plan
    const handlePlanSelect = useCallback((plan) => {
        setPageMessage({ text: '', type: '' });
        setIsCardActionLoading(true);

        if (auth.currentUser?.subscription?.planName === plan.tierName && plan.actionType === 'purchase') {
            setPageMessage({ text: `You are already subscribed to the ${plan.tierName} plan.`, type: 'info' });
            setIsCardActionLoading(false);
            return;
        }

        // This is the main logic block for purchase actions
        if (plan.actionType === 'purchase') {
            if (plan.paymentProcessor === 'lemonsqueezy' && (!plan.lemonSqueezyVariantId || plan.lemonSqueezyVariantId.includes('YOUR_VARIANT_ID'))) {
                 setPageMessage({
                    text: `This plan (${plan.tierName}) via Lemon Squeezy is currently not available (Configuration needed).`,
                    type: 'error'
                 });
                 setIsCardActionLoading(false);
                 return;
            }
            
            // *** THE CRITICAL FIX ***
            // 1. Set the data for the modal first.
            setSelectedPlanForModal(plan);
            // 2. Then, set the state to open the modal. This two-step process is reliable.
            setIsPaymentModalOpen(true);
            
            // 3. Stop the card's loading spinner, as the modal will now be the focus.
            setIsCardActionLoading(false);

        } else if (plan.actionType === 'info' || plan.actionType === 'signup') {
            if (plan.id === 'free_tier_01') {
                setPageMessage({ text: `You've selected the ${plan.tierName} plan. Redirecting...`, type: 'info' });
                // navigate('/signup'); // or '/dashboard' if appropriate
            }
            setIsCardActionLoading(false);
        } else {
            console.warn("[PricingPage] Unknown plan action type:", plan.actionType);
            setIsCardActionLoading(false);
        }
    }, [auth.currentUser, navigate]); // Dependencies for useCallback

    // **FIXED**: The handler to close the modal
    const handleClosePaymentModal = () => {
        // This function ensures a clean close.
        setIsPaymentModalOpen(false);
        setSelectedPlanForModal(null); // Clear the selected plan data
        setIsCardActionLoading(false); // Ensure the card buttons are re-enabled
    };

    return (
        <div className="min-h-screen relative text-white">
            <PageBackground />

            {isCardActionLoading && (
              <div className="fixed inset-0 bg-slate-900 bg-opacity-85 flex flex-col items-center justify-center z-[200]">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-sky-400 mb-4"></div>
                  <p className="text-lg text-sky-300">Processing...</p>
              </div>
            )}

            <PricingView
                pricingTiers={pricingTiers}
                onPlanSelect={handlePlanSelect}
                isViewLoading={isLoadingPlans}
                pageMessage={pageMessage}
                currentPlanName={auth.currentUser?.subscription?.planName}
                isCardActionLoading={isCardActionLoading}
            />

            {/* **FIXED**: More robust conditional rendering for the modal */}
            {/* This JSX now correctly waits for both the 'open' signal and the plan data to be ready */}
            {isPaymentModalOpen && selectedPlanForModal && (
                <PaymentModal
                    isOpen={isPaymentModalOpen}
                    onClose={handleClosePaymentModal}
                    title={`Confirm: ${selectedPlanForModal.tierName}`}
                    selectedPlan={selectedPlanForModal}
                />
            )}

            <footer className="text-center text-xs text-slate-500 pb-8 pt-4 relative z-10">
                Payments processed securely. Manage your subscription in your account settings.
            </footer>
        </div>
    );
};

PricingPage.displayName = 'PricingPage';

export default PricingPage;