// common/user/limit/incrementBusinessQACount.js
import User from '../../../../models/User.js';
import {
    ensureSubscription,
    FREE_TIER_PLAN_NAME_BACKEND,
    PRO_PLAN_NAME_BACKEND,
} from './planConfig.js';
import planConfigService from '../../../../services/planConfigService.js';

export const incrementBusinessQACount = async (req, res) => {
    try {
        let user = await User.findById(req.user.id);
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }
        user = ensureSubscription(user);

        const { subscription } = user;
        const { planName } = subscription;
        const now = new Date();

        if (planName === FREE_TIER_PLAN_NAME_BACKEND) {
            const limit = await planConfigService.getLimit(planName, 'businessQA');
            if (subscription.freeTierBusinessQACount >= limit) {
                return res.status(403).json({ message: 'Starter plan Q&A limit reached. Please upgrade.' });
            }
            subscription.freeTierBusinessQACount++;
        } else if (planName === PRO_PLAN_NAME_BACKEND) {
            const limit = await planConfigService.getLimit(planName, 'businessQA');
            let lastReset = subscription.businessQADailyReset || new Date(0);

            // Check if a new day has started (ignoring time)
            const lastResetDay = new Date(lastReset).setHours(0, 0, 0, 0);
            const currentDay = new Date(now).setHours(0, 0, 0, 0);

            if (currentDay > lastResetDay) {
                // If it's a new day, reset the count and the date
                subscription.proTierBusinessQACount = 1;
                subscription.businessQADailyReset = now;
            } else {
                // If still the same day, check the limit
                if (subscription.proTierBusinessQACount >= limit) {
                    return res.status(403).json({ message: `Pro plan limit of ${limit} Q&A questions per day reached.` });
                }
                subscription.proTierBusinessQACount++;
            }
        } else {
            return res.status(400).json({ message: "User plan does not support this feature." });
        }

        await user.save();
        res.json({
            message: "Q&A count successfully incremented.",
            subscription: user.subscription,
        });

    } catch (error) {
        console.error('Increment Business Q&A Count Error:', error);
        res.status(500).json({ message: 'Server error while incrementing business Q&A count.' });
    }
};