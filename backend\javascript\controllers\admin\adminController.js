// controllers/admin/adminController.js
import User from '../../models/User.js';
import AdminSettings from '../../models/AdminSettings.js';
import SubscriptionLimits from '../../models/SubscriptionLimits.js';

import cacheService from '../../services/cacheService.js';
import planConfigService from '../../services/planConfigService.js';

/**
 * Get all users for admin dashboard
 */
export const getAllUsers = async (req, res) => {
    try {
        const { page = 1, limit = 10, search = '', sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
        
        // Build search query
        const searchQuery = search ? {
            $or: [
                { name: { $regex: search, $options: 'i' } },
                { email: { $regex: search, $options: 'i' } }
            ]
        } : {};

        // Calculate pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);
        
        // Build sort object
        const sort = {};
        sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

        // Get users with pagination
        const users = await User.find(searchQuery)
            .select('-password') // Exclude password field
            .sort(sort)
            .skip(skip)
            .limit(parseInt(limit));

        // Get total count for pagination
        const totalUsers = await User.countDocuments(searchQuery);
        const totalPages = Math.ceil(totalUsers / parseInt(limit));

        res.json({
            users,
            pagination: {
                currentPage: parseInt(page),
                totalPages,
                totalUsers,
                hasNextPage: page < totalPages,
                hasPrevPage: page > 1
            }
        });
    } catch (error) {
        console.error('Get all users error:', error);
        res.status(500).json({ error: 'Failed to fetch users' });
    }
};

/**
 * Get user by ID
 */
export const getUserById = async (req, res) => {
    try {
        const { id } = req.params;
        const user = await User.findById(id).select('-password');
        
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        res.json({ user });
    } catch (error) {
        console.error('Get user by ID error:', error);
        res.status(500).json({ error: 'Failed to fetch user' });
    }
};

/**
 * Update user (admin can update any user)
 */
export const updateUser = async (req, res) => {
    try {
        const { id } = req.params;
        const { name, email, isAdmin, isVerified } = req.body;

        // Validate ObjectId format
        if (!id.match(/^[0-9a-fA-F]{24}$/)) {
            return res.status(400).json({ error: 'Invalid user ID format' });
        }

        // Prevent admin from removing their own admin status
        if (id === req.user._id.toString() && isAdmin === false) {
            return res.status(400).json({
                error: 'You cannot remove your own admin privileges'
            });
        }

        // Find user and check if exists
        const user = await User.findById(id);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Store original values for logging
        const originalValues = {
            name: user.name,
            email: user.email,
            isAdmin: user.isAdmin,
            isVerified: user.isVerified
        };

        // Update user fields only if provided
        if (name !== undefined && name !== user.name) user.name = name;
        if (email !== undefined && email !== user.email) user.email = email;
        if (isAdmin !== undefined && isAdmin !== user.isAdmin) user.isAdmin = isAdmin;
        if (isVerified !== undefined && isVerified !== user.isVerified) user.isVerified = isVerified;

        // Save changes and wait for completion
        await user.save();

        // Verify the update by fetching fresh data
        const updatedUser = await User.findById(id).select('-password');

        if (!updatedUser) {
            return res.status(500).json({ error: 'User update verification failed' });
        }

        // Log the update for audit purposes
        console.log(`Admin ${req.user.email} updated user ${originalValues.email} (ID: ${id})`);
        console.log('Changes:', {
            name: originalValues.name !== updatedUser.name ? `${originalValues.name} → ${updatedUser.name}` : 'unchanged',
            email: originalValues.email !== updatedUser.email ? `${originalValues.email} → ${updatedUser.email}` : 'unchanged',
            isAdmin: originalValues.isAdmin !== updatedUser.isAdmin ? `${originalValues.isAdmin} → ${updatedUser.isAdmin}` : 'unchanged',
            isVerified: originalValues.isVerified !== updatedUser.isVerified ? `${originalValues.isVerified} → ${updatedUser.isVerified}` : 'unchanged'
        });

        res.json({
            user: updatedUser,
            message: 'User updated successfully',
            changes: {
                name: originalValues.name !== updatedUser.name,
                email: originalValues.email !== updatedUser.email,
                isAdmin: originalValues.isAdmin !== updatedUser.isAdmin,
                isVerified: originalValues.isVerified !== updatedUser.isVerified
            }
        });
    } catch (error) {
        console.error('Update user error:', error);
        if (error.code === 11000) {
            // Duplicate key error (email already exists)
            const field = Object.keys(error.keyValue)[0];
            res.status(400).json({ error: `${field} already exists` });
        } else if (error.name === 'ValidationError') {
            // Mongoose validation error
            const messages = Object.values(error.errors).map(e => e.message);
            res.status(400).json({ error: messages.join(', ') });
        } else {
            res.status(500).json({ error: 'Failed to update user' });
        }
    }
};

/**
 * Delete user (admin can delete any user except themselves)
 */
export const deleteUser = async (req, res) => {
    try {
        const { id } = req.params;

        // Validate ObjectId format
        if (!id.match(/^[0-9a-fA-F]{24}$/)) {
            return res.status(400).json({ error: 'Invalid user ID format' });
        }

        // Prevent admin from deleting themselves
        if (id === req.user._id.toString()) {
            return res.status(400).json({
                error: 'You cannot delete your own account'
            });
        }

        // Check if user exists before deletion
        const user = await User.findById(id);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Perform the deletion and wait for completion
        const deletedUser = await User.findByIdAndDelete(id);

        if (!deletedUser) {
            return res.status(500).json({ error: 'Failed to delete user from database' });
        }

        // Log the deletion for audit purposes
        console.log(`Admin ${req.user.email} deleted user ${user.email} (ID: ${id})`);

        res.json({
            message: 'User deleted successfully',
            deletedUser: {
                id: deletedUser._id,
                email: deletedUser.email,
                name: deletedUser.name
            }
        });
    } catch (error) {
        console.error('Delete user error:', error);
        res.status(500).json({ error: 'Failed to delete user' });
    }
};

/**
 * Get admin users only
 */
export const getAdminUsers = async (req, res) => {
    try {
        const adminUsers = await User.find({ isAdmin: true })
            .select('-password')
            .sort({ createdAt: -1 });

        res.json({ adminUsers });
    } catch (error) {
        console.error('Get admin users error:', error);
        res.status(500).json({ error: 'Failed to fetch admin users' });
    }
};

/**
 * Get system analytics
 */
export const getSystemAnalytics = async (req, res) => {
    try {
        // Get user statistics
        const totalUsers = await User.countDocuments();
        const verifiedUsers = await User.countDocuments({ isVerified: true });
        const adminUsers = await User.countDocuments({ isAdmin: true });
        const unverifiedUsers = totalUsers - verifiedUsers;

        // Get registration statistics for the last 30 days
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        
        const recentRegistrations = await User.countDocuments({
            createdAt: { $gte: thirtyDaysAgo }
        });

        // Get admin settings
        const adminSettings = await AdminSettings.getSettings();

        res.json({
            userStats: {
                totalUsers,
                verifiedUsers,
                unverifiedUsers,
                adminUsers,
                recentRegistrations
            },
            systemInfo: {
                hasInitialAdmin: adminSettings.hasInitialAdmin,
                systemAdminEmail: adminSettings.systemAdminEmail,
                allowNewAdminRegistration: adminSettings.allowNewAdminRegistration,
                maintenanceMode: adminSettings.maintenanceMode
            }
        });
    } catch (error) {
        console.error('Get system analytics error:', error);
        res.status(500).json({ error: 'Failed to fetch system analytics' });
    }
};

/**
 * Update system settings
 */
export const updateSystemSettings = async (req, res) => {
    try {
        const { allowNewAdminRegistration, maintenanceMode, systemName } = req.body;
        
        const settings = await AdminSettings.getSettings();
        
        if (allowNewAdminRegistration !== undefined) {
            settings.allowNewAdminRegistration = allowNewAdminRegistration;
        }
        if (maintenanceMode !== undefined) {
            settings.maintenanceMode = maintenanceMode;
        }
        if (systemName !== undefined) {
            settings.systemName = systemName;
        }

        await settings.save();
        
        res.json({ 
            settings, 
            message: 'System settings updated successfully' 
        });
    } catch (error) {
        console.error('Update system settings error:', error);
        res.status(500).json({ error: 'Failed to update system settings' });
    }
};

/**
 * Check if admin registration is allowed
 */
export const checkAdminRegistrationStatus = async (req, res) => {
    try {
        const canRegister = await AdminSettings.canRegisterAdmin();
        const settings = await AdminSettings.getSettings();

        res.json({
            canRegisterAdmin: canRegister,
            hasInitialAdmin: settings.hasInitialAdmin
        });
    } catch (error) {
        console.error('Check admin registration status error:', error);
        res.status(500).json({ error: 'Failed to check admin registration status' });
    }
};

/**
 * Get actual plan configuration (dynamic, database-backed)
 */
export const getPlanConfiguration = async (req, res) => {
    try {
        const planConfigs = await planConfigService.getAllConfigs();

        res.json({
            success: true,
            planConfig: planConfigs
        });
    } catch (error) {
        console.error('Get plan configuration error:', error);
        res.status(500).json({ error: 'Failed to fetch plan configuration' });
    }
};

/**
 * Update plan configuration (dynamic database updates)
 */
export const updatePlanConfiguration = async (req, res) => {
    try {
        const { planName, limits } = req.body;

        if (!planName || !limits) {
            return res.status(400).json({
                error: 'Missing required fields: planName and limits'
            });
        }

        // Validate limits
        const validation = planConfigService.validateLimits(limits);
        if (!validation.isValid) {
            return res.status(400).json({
                error: 'Invalid limits data',
                details: validation.errors
            });
        }

        // Update configuration
        const updatedConfig = await planConfigService.updatePlanConfig(
            planName,
            limits,
            req.user?._id
        );

        res.json({
            success: true,
            message: `Plan configuration for ${planName} updated successfully`,
            config: updatedConfig
        });
    } catch (error) {
        console.error('Update plan configuration error:', error);
        res.status(500).json({ error: error.message || 'Failed to update plan configuration' });
    }
};

/**
 * Get all subscription plans and their limits
 */
export const getSubscriptionPlans = async (req, res) => {
    try {
        // Try to get from cache first
        let plans = cacheService.getSubscriptionPlans();

        if (!plans) {
            console.log('Admin: Subscription plans not in cache, fetching from database');

            // Initialize default plans if they don't exist
            await SubscriptionLimits.initializeDefaultPlans();

            // Only fetch Starter and Pro plans, exclude Enterprise
            plans = await SubscriptionLimits.find({
                isActive: true,
                planName: { $in: ['Starter', 'Pro'] } // Only allow Starter and Pro
            }).sort({ sortOrder: 1 });

            // Cache the results
            cacheService.setSubscriptionPlans(plans);
        } else {
            console.log('Admin: Subscription plans served from cache');
        }

        res.json({ plans });
    } catch (error) {
        console.error('Get subscription plans error:', error);
        res.status(500).json({ error: 'Failed to fetch subscription plans' });
    }
};

/**
 * Get specific subscription plan by name
 */
export const getSubscriptionPlan = async (req, res) => {
    try {
        const { planName } = req.params;

        // Try to get from cache first
        let plan = cacheService.getSubscriptionPlan(planName);

        if (!plan) {
            console.log(`Admin: Subscription plan "${planName}" not in cache, fetching from database`);

            plan = await SubscriptionLimits.findOne({
                planName,
                isActive: true
            });

            if (plan) {
                // Cache the result
                cacheService.setSubscriptionPlan(planName, plan);
            }
        } else {
            console.log(`Admin: Subscription plan "${planName}" served from cache`);
        }

        if (!plan) {
            return res.status(404).json({ error: 'Subscription plan not found' });
        }

        res.json({ plan });
    } catch (error) {
        console.error('Get subscription plan error:', error);
        res.status(500).json({ error: 'Failed to fetch subscription plan' });
    }
};

/**
 * Update subscription plan limits
 */
export const updateSubscriptionPlan = async (req, res) => {
    try {
        const { planName } = req.params;
        const updateData = req.body;

        const plan = await SubscriptionLimits.findOne({ planName });

        if (!plan) {
            return res.status(404).json({ error: 'Subscription plan not found' });
        }

        // Update plan data
        if (updateData.displayName) plan.displayName = updateData.displayName;
        if (updateData.description !== undefined) plan.description = updateData.description;
        if (updateData.price) plan.price = { ...plan.price, ...updateData.price };
        if (updateData.features !== undefined) plan.features = updateData.features;
        if (updateData.limits) plan.limits = { ...plan.limits, ...updateData.limits };
        if (updateData.isActive !== undefined) plan.isActive = updateData.isActive;
        if (updateData.sortOrder !== undefined) plan.sortOrder = updateData.sortOrder;

        await plan.save();

        // Invalidate cache for this plan and all plans
        cacheService.invalidateSubscriptionPlan(planName);

        // Also invalidate stats cache since plan changes affect stats
        cacheService.invalidateSubscriptionStats();

        console.log(`Admin: ${planName} plan updated successfully`);

        res.json({
            plan,
            message: `${planName} plan updated successfully`
        });
    } catch (error) {
        console.error('Update subscription plan error:', error);
        res.status(500).json({ error: 'Failed to update subscription plan' });
    }
};

/**
 * Reset subscription plan to default values
 */
export const resetSubscriptionPlan = async (req, res) => {
    try {
        const { planName } = req.params;

        const defaultData = SubscriptionLimits.getDefaultLimits(planName);

        const plan = await SubscriptionLimits.findOneAndUpdate(
            { planName },
            {
                ...defaultData,
                planName // Ensure planName is preserved
            },
            {
                new: true,
                upsert: true
            }
        );

        // Invalidate cache for this plan and all plans
        cacheService.invalidateSubscriptionPlan(planName);

        // Also invalidate stats cache
        cacheService.invalidateSubscriptionStats();

        console.log(`Admin: ${planName} plan reset to default values`);

        res.json({
            plan,
            message: `${planName} plan reset to default values`
        });
    } catch (error) {
        console.error('Reset subscription plan error:', error);
        res.status(500).json({ error: 'Failed to reset subscription plan' });
    }
};

/**
 * Get subscription usage statistics
 */
export const getSubscriptionStats = async (req, res) => {
    try {
        // Get user counts by plan
        const userStats = await User.aggregate([
            {
                $group: {
                    _id: '$subscription.planName',
                    count: { $sum: 1 },
                    verified: {
                        $sum: { $cond: ['$isVerified', 1, 0] }
                    }
                }
            }
        ]);

        // Get total usage statistics
        const totalUsers = await User.countDocuments();
        const activeUsers = await User.countDocuments({ isVerified: true });

        // Calculate revenue estimates (this would be more complex in a real system)
        const plans = await SubscriptionLimits.find({ isActive: true });
        let estimatedMonthlyRevenue = 0;

        for (const stat of userStats) {
            const plan = plans.find(p => p.planName === stat._id);
            if (plan && plan.price.monthly > 0) {
                estimatedMonthlyRevenue += stat.verified * plan.price.monthly;
            }
        }

        res.json({
            userStats,
            totalUsers,
            activeUsers,
            estimatedMonthlyRevenue,
            planDistribution: userStats.map(stat => ({
                planName: stat._id || 'Starter',
                userCount: stat.count,
                verifiedUsers: stat.verified
            }))
        });
    } catch (error) {
        console.error('Get subscription stats error:', error);
        res.status(500).json({ error: 'Failed to fetch subscription statistics' });
    }
};

/**
 * Get cache statistics (admin only)
 */
export const getCacheStats = async (req, res) => {
    try {
        const cacheStats = cacheService.getStats();

        res.json({
            success: true,
            cache: cacheStats,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Get cache stats error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch cache statistics'
        });
    }
};

/**
 * Clear cache (admin only)
 */
export const clearCache = async (req, res) => {
    try {
        const { type } = req.body;

        switch (type) {
            case 'subscription-plans':
                cacheService.invalidateSubscriptionPlans();
                break;
            case 'subscription-stats':
                cacheService.invalidateSubscriptionStats();
                break;
            case 'all':
                cacheService.clearAll();
                break;
            default:
                return res.status(400).json({
                    success: false,
                    error: 'Invalid cache type. Use: subscription-plans, subscription-stats, or all'
                });
        }

        res.json({
            success: true,
            message: `Cache "${type}" cleared successfully`,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Clear cache error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to clear cache'
        });
    }
};

/**
 * Get plan configuration for current user (public endpoint)
 */
export const getUserPlanConfiguration = async (req, res) => {
    try {
        const planConfigs = await planConfigService.getAllConfigs();

        // Return only the limits data without sensitive admin info
        const publicPlanData = planConfigs.map(config => ({
            planName: config.planName,
            displayName: config.displayName,
            limits: config.limits
        }));

        res.json({
            success: true,
            planConfig: publicPlanData
        });
    } catch (error) {
        console.error('Get user plan configuration error:', error);
        res.status(500).json({ error: 'Failed to fetch plan configuration' });
    }
};

/**
 * Get public subscription plans for pricing page (public endpoint)
 */
export const getPublicSubscriptionPlans = async (req, res) => {
    try {
        // Try to get from cache first
        let plans = cacheService.getPublicPlans();

        if (!plans) {
            console.log('Public: Subscription plans not in cache, fetching from database');

            // Initialize default plans if they don't exist
            await SubscriptionLimits.initializeDefaultPlans();

            // Fetch only active Starter and Pro plans, explicitly excluding Enterprise
            const allPlans = await SubscriptionLimits.find({
                isActive: true,
                planName: {
                    $in: ['Starter', 'Pro'],  // Only allow Starter and Pro
                    $not: { $regex: /enterprise/i }  // Explicitly exclude Enterprise
                }
            }).sort({ sortOrder: 1 });

            // Ensure we only have exactly 2 plans and remove duplicates
            const uniquePlans = [];
            const seenPlans = new Set();

            for (const plan of allPlans) {
                if (!seenPlans.has(plan.planName) && (plan.planName === 'Starter' || plan.planName === 'Pro')) {
                    seenPlans.add(plan.planName);
                    uniquePlans.push(plan);
                }
            }

            // Transform for public consumption - remove sensitive data
            plans = uniquePlans.map(plan => ({
                id: plan._id,
                planName: plan.planName,
                tierName: plan.planName, // For compatibility with frontend
                displayName: plan.displayName,
                description: plan.description,
                price: plan.price.monthly === 0 ? 'Free' : `$${plan.price.monthly}`,
                pricePeriod: plan.price.monthly === 0 ? '' : '/month',
                billingCycle: plan.price.monthly === 0 ? '' : 'monthly',
                features: plan.features || [],
                buttonText: plan.price.monthly === 0 ? 'Get Started' : 'Choose Plan',
                actionType: plan.price.monthly === 0 ? 'free' : 'purchase',
                isPopular: plan.planName === 'Pro', // Mark Pro as popular
                currency: 'USD',
                paymentProcessor: 'paypal', // Default payment processor
                sortOrder: plan.sortOrder
            }));

            // Ensure we have exactly 2 plans (Starter and Pro)
            if (plans.length !== 2) {
                console.warn(`Expected 2 plans, but got ${plans.length}. Creating default plans.`);
                plans = [
                    {
                        id: 'starter-default',
                        planName: 'Starter',
                        tierName: 'Starter',
                        displayName: 'Starter Plan',
                        description: 'For individuals and small projects. Get started for free.',
                        price: 'Free',
                        pricePeriod: '',
                        billingCycle: '',
                        features: [
                            '5 PDF uploads per month',
                            '1 business plan per month',
                            '1 investor pitch per month',
                            '10 business Q&A per day',
                            '25 chat messages per day',
                            '500MB storage',
                            'Community support'
                        ],
                        buttonText: 'Get Started',
                        actionType: 'free',
                        isPopular: false,
                        currency: 'USD',
                        paymentProcessor: 'paypal',
                        sortOrder: 1
                    },
                    {
                        id: 'pro-default',
                        planName: 'Pro',
                        tierName: 'Pro',
                        displayName: 'Pro Plan',
                        description: 'For professionals and growing businesses with higher needs.',
                        price: '$19.99',
                        pricePeriod: '/month',
                        billingCycle: 'monthly',
                        features: [
                            '50 PDF uploads per month',
                            '10 business plans per month',
                            '10 investor pitches per month',
                            'Unlimited business Q&A',
                            'Unlimited chat messages',
                            '5GB storage',
                            'Advanced analytics',
                            'Priority support'
                        ],
                        buttonText: 'Choose Plan',
                        actionType: 'purchase',
                        isPopular: true,
                        currency: 'USD',
                        paymentProcessor: 'paypal',
                        sortOrder: 2
                    }
                ];
            }

            // Cache the results for 30 minutes
            cacheService.setPublicPlans(plans, 1800);
        } else {
            console.log('Public: Subscription plans served from cache');
        }

        res.json({
            success: true,
            plans: plans
        });
    } catch (error) {
        console.error('Get public subscription plans error:', error);
        res.status(500).json({ error: 'Failed to fetch subscription plans' });
    }
};

/**
 * Clear subscription plans cache (admin endpoint)
 */
export const clearSubscriptionPlansCache = async (req, res) => {
    try {
        cacheService.invalidateSubscriptionPlans();
        res.json({
            success: true,
            message: 'Subscription plans cache cleared successfully'
        });
    } catch (error) {
        console.error('Clear subscription plans cache error:', error);
        res.status(500).json({ error: 'Failed to clear cache' });
    }
};

/**
 * Remove Enterprise plans from database (admin endpoint)
 */
export const removeEnterprisePlans = async (req, res) => {
    try {
        // Remove any Enterprise plans from the database
        const result = await SubscriptionLimits.deleteMany({
            planName: { $regex: /enterprise/i }
        });

        // Clear cache after deletion
        cacheService.invalidateSubscriptionPlans();

        res.json({
            success: true,
            message: `Removed ${result.deletedCount} Enterprise plan(s) from database`,
            deletedCount: result.deletedCount
        });
    } catch (error) {
        console.error('Remove Enterprise plans error:', error);
        res.status(500).json({ error: 'Failed to remove Enterprise plans' });
    }
};
