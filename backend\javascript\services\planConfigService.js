// services/planConfigService.js
import PlanConfiguration from '../models/PlanConfiguration.js';

class PlanConfigService {
    constructor() {
        this.cache = new Map();
        this.cacheExpiry = 5 * 60 * 1000; // 5 minutes
        this.lastCacheUpdate = new Map();
    }

    // Initialize the service and load default configurations
    async initialize() {
        try {
            await PlanConfiguration.initializeDefaults();
            await this.refreshCache();
            console.log('PlanConfigService initialized successfully');
        } catch (error) {
            console.error('Failed to initialize PlanConfigService:', error);
            throw error;
        }
    }

    // Refresh the entire cache
    async refreshCache() {
        try {
            const configs = await PlanConfiguration.getAllConfigs();
            
            this.cache.clear();
            this.lastCacheUpdate.clear();
            
            for (const config of configs) {
                this.cache.set(config.planName, config.toObject());
                this.lastCacheUpdate.set(config.planName, Date.now());
            }
            
            console.log(`Cached ${configs.length} plan configurations`);
        } catch (error) {
            console.error('Failed to refresh plan config cache:', error);
            throw error;
        }
    }

    // Check if cache is expired for a specific plan
    isCacheExpired(planName) {
        const lastUpdate = this.lastCacheUpdate.get(planName);
        if (!lastUpdate) return true;
        
        return (Date.now() - lastUpdate) > this.cacheExpiry;
    }

    // Get configuration for a specific plan
    async getPlanConfig(planName) {
        try {
            // Check cache first
            if (this.cache.has(planName) && !this.isCacheExpired(planName)) {
                return this.cache.get(planName);
            }

            // Fetch from database
            const config = await PlanConfiguration.getPlanConfig(planName);
            
            if (config) {
                this.cache.set(planName, config.toObject());
                this.lastCacheUpdate.set(planName, Date.now());
                return config.toObject();
            }

            return null;
        } catch (error) {
            console.error(`Failed to get plan config for ${planName}:`, error);
            return null;
        }
    }

    // Get all configurations
    async getAllConfigs() {
        try {
            // Check if we need to refresh cache
            const needsRefresh = Array.from(this.lastCacheUpdate.values()).some(lastUpdate => 
                (Date.now() - lastUpdate) > this.cacheExpiry
            );

            if (needsRefresh || this.cache.size === 0) {
                await this.refreshCache();
            }

            return Array.from(this.cache.values());
        } catch (error) {
            console.error('Failed to get all plan configs:', error);
            return [];
        }
    }

    // Update plan configuration
    async updatePlanConfig(planName, limits, updatedBy = null) {
        try {
            const config = await PlanConfiguration.updatePlanConfig(planName, limits, updatedBy);
            
            // Update cache
            this.cache.set(planName, config.toObject());
            this.lastCacheUpdate.set(planName, Date.now());
            
            console.log(`Updated plan configuration for ${planName}:`, limits);
            return config.toObject();
        } catch (error) {
            console.error(`Failed to update plan config for ${planName}:`, error);
            throw error;
        }
    }

    // Get limit for a specific plan and feature (backward compatibility)
    async getLimit(planName, feature) {
        try {
            const config = await this.getPlanConfig(planName);
            
            if (!config) {
                console.warn(`Plan configuration for ${planName} not found, returning 0`);
                return 0;
            }
            
            return config.limits[feature] || 0;
        } catch (error) {
            console.error(`Failed to get limit for ${planName}.${feature}:`, error);
            return 0;
        }
    }

    // Check if a limit should be enforced for a plan
    shouldEnforceLimit(planName) {
        return planName === 'Starter' || planName === 'Pro';
    }

    // Get plan names
    getPlanNames() {
        return {
            FREE_TIER: 'Starter',
            PRO_TIER: 'Pro'
        };
    }

    // Clear cache (useful for testing or manual refresh)
    clearCache() {
        this.cache.clear();
        this.lastCacheUpdate.clear();
        console.log('Plan configuration cache cleared');
    }

    // Get cache statistics
    getCacheStats() {
        return {
            cacheSize: this.cache.size,
            cachedPlans: Array.from(this.cache.keys()),
            lastUpdates: Object.fromEntries(this.lastCacheUpdate),
            cacheExpiry: this.cacheExpiry
        };
    }

    // Validate limits object
    validateLimits(limits) {
        const requiredFields = ['pdfUpload', 'businessPlan', 'investorPitch', 'businessQA', 'message'];
        const errors = [];

        for (const field of requiredFields) {
            if (!(field in limits)) {
                errors.push(`Missing required field: ${field}`);
            } else if (typeof limits[field] !== 'number' || limits[field] < 0) {
                errors.push(`Invalid value for ${field}: must be a non-negative number`);
            }
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }
}

// Create singleton instance
const planConfigService = new PlanConfigService();

export default planConfigService;
