// scripts/migrate-add-isAdmin.js
import mongoose from 'mongoose';
import User from '../models/User.js';
import { config } from 'dotenv';

// Load environment variables
config();

const MONGO_URI = process.env.MONGO_URI;

if (!MONGO_URI) {
    console.error('MONGO_URI is not defined in environment variables');
    process.exit(1);
}

/**
 * Migration script to add isAdmin field to existing users
 * This script will:
 * 1. Connect to the database
 * 2. Find all users without the isAdmin field
 * 3. Add isAdmin: false to all existing users
 * 4. Close the connection
 */
const migrateUsers = async () => {
    try {
        console.log('Connecting to MongoDB...');
        await mongoose.connect(MONGO_URI);
        console.log('Connected to MongoDB successfully');

        // Find all users that don't have the isAdmin field
        const usersWithoutIsAdmin = await User.find({ isAdmin: { $exists: false } });
        
        console.log(`Found ${usersWithoutIsAdmin.length} users without isAdmin field`);

        if (usersWithoutIsAdmin.length === 0) {
            console.log('All users already have the isAdmin field. No migration needed.');
            return;
        }

        // Update all users to have isAdmin: false by default
        const result = await User.updateMany(
            { isAdmin: { $exists: false } },
            { $set: { isAdmin: false } }
        );

        console.log(`Migration completed successfully!`);
        console.log(`Updated ${result.modifiedCount} users with isAdmin: false`);

        // Verify the migration
        const remainingUsers = await User.find({ isAdmin: { $exists: false } });
        if (remainingUsers.length === 0) {
            console.log('✅ Migration verification passed: All users now have the isAdmin field');
        } else {
            console.log(`⚠️  Warning: ${remainingUsers.length} users still don't have the isAdmin field`);
        }

    } catch (error) {
        console.error('Migration failed:', error);
        process.exit(1);
    } finally {
        await mongoose.connection.close();
        console.log('Database connection closed');
    }
};

// Run the migration
migrateUsers()
    .then(() => {
        console.log('Migration script completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Migration script failed:', error);
        process.exit(1);
    });
