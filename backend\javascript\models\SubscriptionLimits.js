// models/SubscriptionLimits.js
import mongoose from 'mongoose';
import cacheService from '../services/cacheService.js';

// Default limits configuration - The master source for default values
const defaultLimits = {
  Starter: {
    planName: 'Starter',
    displayName: 'Starter Plan',
    description: 'For individuals and small projects. Get started for free.',
    price: { monthly: 0, yearly: 0 },
    features: [
      '5 PDF uploads per month',
      '1 business plan per month',
      '1 investor pitch per month',
      '10 business Q&A per day',
      '25 chat messages per day',
      '500MB storage',
      'Community support'
    ],
    limits: {
      pdfUploads: { monthly: 5, daily: -1 }, // Default monthly upload limit for Starter
      businessPlans: { monthly: 1, daily: -1 },
      investorPitches: { monthly: 1, daily: -1 },
      businessQA: { daily: 10, monthly: 100 },
      chatMessages: { daily: 25, monthly: 500 },
      storage: { total: 500 }, // in MB
      advancedAnalytics: false,
      prioritySupport: false,
      customBranding: false,
    },
    isActive: true,
    sortOrder: 1,
  },
  Pro: {
    planName: 'Pro',
    displayName: 'Pro Plan',
    description: 'For professionals and growing businesses with higher needs.',
    price: { monthly: 19.99, yearly: 199.99 },
    features: [
      '50 PDF uploads per month',
      '10 business plans per month',
      '10 investor pitches per month',
      'Unlimited business Q&A',
      'Unlimited chat messages',
      '5GB storage',
      'Advanced analytics',
      'Priority support'
    ],
    limits: {
      pdfUploads: { monthly: 50, daily: -1 }, // Default monthly upload limit for Pro
      businessPlans: { monthly: 10, daily: -1 },
      investorPitches: { monthly: 10, daily: -1 },
      businessQA: { daily: 100, monthly: -1 }, // -1 for unlimited
      chatMessages: { daily: 200, monthly: -1 }, // -1 for unlimited
      storage: { total: 5000 }, // in MB
      advancedAnalytics: true,
      prioritySupport: true,
      customBranding: false,
    },
    isActive: true,
    sortOrder: 2,
  },
};

const limitsSchema = new mongoose.Schema({
  monthly: { type: Number, default: 0 },
  daily: { type: Number, default: 0 },
}, { _id: false });

const storageSchema = new mongoose.Schema({
    total: { type: Number, default: 0 } // in MB
}, { _id: false });

const priceSchema = new mongoose.Schema({
    monthly: { type: Number, default: 0 },
    yearly: { type: Number, default: 0 }
}, { _id: false });

const subscriptionLimitsSchema = new mongoose.Schema({
  planName: { type: String, required: true, unique: true, enum: ['Starter', 'Pro'] },
  displayName: { type: String, required: true },
  description: { type: String, default: '' },
  price: { type: priceSchema, default: () => ({}) },
  features: { type: [String], default: [] },
  limits: {
    pdfUploads: { type: limitsSchema, default: () => ({ monthly: 0, daily: -1 }) },
    businessPlans: { type: limitsSchema, default: () => ({ monthly: 0, daily: -1 }) },
    investorPitches: { type: limitsSchema, default: () => ({ monthly: 0, daily: -1 }) },
    businessQA: { type: limitsSchema, default: () => ({ monthly: 0, daily: 0 }) },
    chatMessages: { type: limitsSchema, default: () => ({ monthly: 0, daily: 0 }) },
    storage: { type: storageSchema, default: () => ({}) },
    advancedAnalytics: { type: Boolean, default: false },
    prioritySupport: { type: Boolean, default: false },
    customBranding: { type: Boolean, default: false },
  },
  isActive: { type: Boolean, default: true },
  sortOrder: { type: Number, default: 99 },
}, { timestamps: true });

// Static method to get the default values for a plan
subscriptionLimitsSchema.statics.getDefaultLimits = function(planName) {
  return defaultLimits[planName] || null;
};

// Static method to initialize or update default plans in the database
subscriptionLimitsSchema.statics.initializeDefaultPlans = async function() {
  console.log('Initializing or verifying default subscription plans...');
  const plans = Object.values(defaultLimits);
  let plansCreated = 0;
  for (const planData of plans) {
    const existingPlan = await this.findOne({ planName: planData.planName });
    if (!existingPlan) {
      await this.create(planData);
      plansCreated++;
      console.log(`Created default plan: ${planData.planName}`);
    }
  }
  if (plansCreated > 0) {
      console.log(`${plansCreated} default plan(s) created.`);
      cacheService.invalidateSubscriptionPlans(); // Invalidate cache after creation
  } else {
      console.log('All default plans already exist.');
  }
};

const SubscriptionLimits = mongoose.model('SubscriptionLimits', subscriptionLimitsSchema);

export default SubscriptionLimits;