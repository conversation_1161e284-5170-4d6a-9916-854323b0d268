// DOSKYHomepage.jsx
import React, { useState, useEffect, useCallback, lazy, Suspense, useMemo } from 'react';

// --- Eagerly Imported Components ---
// Components visible "above the fold" are imported directly for faster initial paint.
import HeroSection from '../../../components/common/home/<USER>';

// --- Lazily Loaded Components ---
// Components below the fold are lazy-loaded to reduce the initial bundle size.
const FeaturesShowcase = lazy(() => import('../../../components/common/home/<USER>'));
const HowItWorks = lazy(() => import('../../../components/common/home/<USER>'));
const PricingPlans = lazy(() => import('../../../components/common/home/<USER>'));
const CallToAction = lazy(() => import('../../../components/common/home/<USER>'));
const PageFooter = lazy(() => import('../../../components/common/home/<USER>'));

// --- Static Data ---
// Moved outside the component to prevent re-declaration on every render.
const featuresData = [
  { iconName: "MessageCircle", title: "PdfChat", description: "Converse with your PDFs...", color: "from-purple-500 to-pink-500" },
  { iconName: "Brain", title: "PdfToMindMap", description: "Transform complex documents...", color: "from-blue-500 to-cyan-500" },
  { iconName: "FileText", title: "PdfSummary", description: "Get intelligent summaries...", color: "from-green-500 to-emerald-500" }
];

const statsData = [
  { number: "50K+", label: "Active Users", iconName: "Users" },
  { number: "1M+", label: "Documents Processed", iconName: "FileText" },
  { number: "99.9%", label: "Uptime", iconName: "TrendingUp" },
  { number: "24/7", label: "AI Support", iconName: "Clock" }
];

// API Base URL
const API_BASE_URL = import.meta.env.VITE_NODE_BACKEND_URL || 'http://localhost:3001';
// --- End of Static Data ---

// --- Fallback Components ---
// Minimalist loaders to reduce layout shift and visual noise.
const LoadingSpinner = () => (
  <div className="flex justify-center items-center py-20" aria-busy="true">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
  </div>
);

const MinimalFooterLoader = () => <div className="h-20" />; // Placeholder with a height to prevent layout jump.


const DOSKYHomepage = () => {
  const [activeFeature, setActiveFeature] = useState(0);
  const [dynamicPricingData, setDynamicPricingData] = useState([]);
  const [isPricingLoading, setIsPricingLoading] = useState(true);

  // Fetch dynamic pricing data
  const fetchPricingData = useCallback(async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/admin/public/subscription-plans`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.plans) {
          // Transform API data to match expected format
          const transformedData = data.plans.map(plan => ({
            name: plan.planName,
            price: plan.price,
            description: plan.description,
            features: plan.features || [],
            popular: plan.isPopular || false
          }));
          setDynamicPricingData(transformedData);
        }
      }
    } catch (error) {
      console.error('Error fetching pricing data:', error);
      // Fallback to static data if API fails
      setDynamicPricingData([
        { name: "Starter", price: "Free", description: "For personal use", features: ["5 docs/month", "Basic AI", "Community support"], popular: false },
        { name: "Pro", price: "$29", description: "For professionals", features: ["Unlimited docs", "All AI tools", "Priority support", "Analytics"], popular: true }
      ]);
    } finally {
      setIsPricingLoading(false);
    }
  }, []);

  // The feature rotation logic remains in useEffect. Its dependency array is empty
  // as featuresData.length is a constant, ensuring the effect runs only once on mount.
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveFeature(prev => (prev + 1) % featuresData.length);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  // Fetch pricing data on component mount
  useEffect(() => {
    fetchPricingData();
  }, [fetchPricingData]);

  // --- Memoized Event Handlers ---
  // useCallback is used to memoize these functions, preventing child components
  // from re-rendering unnecessarily if they are wrapped in React.memo.
  const handleStartTrial = useCallback(() => { /* Analytics or tracking logic can go here */ }, []);
  const handleWatchDemo = useCallback(() => { /* Analytics or tracking logic can go here */ }, []);
  const handlePlanSelect = useCallback((planName) => { /* Analytics or tracking logic for plan selection */ }, []);
  const handleTalkToSales = useCallback(() => { /* Analytics or tracking logic can go here */ }, []);
  
  // State setters from useState are already memoized by React, so
  // `setActiveFeature` does not need to be wrapped in useCallback.

  // --- Memoized Props ---
  // The props object for HeroSection is memoized using useMemo. This is beneficial
  // if HeroSection is a memoized component, as it will prevent re-renders caused by
  // the props object being recreated on every render of DOSKYHomepage.
  const heroProps = useMemo(() => ({
    stats: statsData,
    onStartTrialClick: handleStartTrial,
    onWatchDemoClick: handleWatchDemo,
  }), [handleStartTrial, handleWatchDemo]);


  return (
    <div className="min-h-screen bg-black text-white relative overflow-x-hidden">
      {/* Decorative background element. aria-hidden="true" is good for accessibility. */}
      <div
        aria-hidden="true"
        className="absolute top-0 left-0 w-[clamp(300px,50vw,600px)] h-[clamp(300px,50vh,600px)]
                   bg-purple-600 blur-[150px] rounded-full
                   transform -translate-x-1/2 -translate-y-1/2 pointer-events-none opacity-30"
      ></div>

      <HeroSection {...heroProps} />

      {/* Each lazy-loaded component is wrapped in Suspense with a fallback. */}
      <Suspense fallback={<LoadingSpinner />}>
        <FeaturesShowcase
          features={featuresData}
          activeFeature={activeFeature}
          setActiveFeature={setActiveFeature}
        />
      </Suspense>

      <Suspense fallback={<LoadingSpinner />}>
        <HowItWorks />
      </Suspense>

      <Suspense fallback={<LoadingSpinner />}>
        <PricingPlans
          plans={dynamicPricingData}
          onPlanSelect={handlePlanSelect}
        />
      </Suspense>

      <Suspense fallback={<LoadingSpinner />}>
        <CallToAction
          onStartTrialClick={handleStartTrial}
          onTalkToSalesClick={handleTalkToSales}
        />
      </Suspense>
      
      <Suspense fallback={<MinimalFooterLoader />}>
        <PageFooter />
      </Suspense>
    </div>
  );
};

// For this optimization to be fully effective, ensure your child components
// are wrapped in React.memo where appropriate. For example:
//
// In '.../HeroSection.jsx':
// import React, { memo } from 'react';
// const HeroSection = (props) => { ... };
// export default memo(HeroSection);
//
// Do the same for FeaturesShowcase, PricingPlans, and CallToAction.

export default DOSKYHomepage;