// controllers/businessPlan/PlanGeneratorController.js
import { generateContent } from '../../../services/geminiBusinessService.js';
import { buildFullBusinessPlanPrompt } from './PlanGenratorPrompts.js';
import axios from 'axios';
import User from '../../../models/User.js';
import {
    ensureSubscription,
    FREE_TIER_PLAN_NAME_BACKEND,
    PRO_PLAN_NAME_BACKEND,
    shouldEnforceLimit,
    getLimit,
} from '../../common/user/limit/planConfig.js';
import planConfigService from '../../../services/planConfigService.js';

const fetchMarketData = async (query) => {
    if (!query) return [];
    try {
        const response = await axios.request({
            method: 'GET',
            url: 'https://dataservices-market-research-library-v1.p.rapidapi.com/api.trade.gov/v2/market_research_library/search',
            params: { q: query, limit: 5 },
            headers: {
                'x-rapidapi-key': process.env.RAPIDAPI_KEY,
                'x-rapidapi-host': 'dataservices-market-research-library-v1.p.rapidapi.com'
            }
        });
        return response.data?.results || [];
    } catch (error) {
        console.error('RapidAPI Market Data Request Failed:', error.message);
        return [];
    }
};

export const generateFullBusinessReport = async (req, res) => {
    // 1. Fetch user and validate form data
    const user = await User.findById(req.user.id);
    if (!user) {
        return res.status(404).json({ error: 'User not found.' });
    }

    const formData = req.body;
    const { planType, idea, audience, profit, problem, language, period, budget, industry } = formData;

    if (!planType || !language || !period) {
        return res.status(400).json({ error: 'Core planning parameters are missing.' });
    }
    if (planType === 'user' && (!idea || !audience || !profit || !problem)) {
        return res.status(400).json({ error: 'A complete business context is required to generate a plan.' });
    } else if (planType === 'ai' && (!budget || !industry)) {
        return res.status(400).json({ error: 'Investment Budget and Industry are required for the AI to generate an idea.' });
    }

    // 2. Check usage limit
    const userWithSub = ensureSubscription(user);
    const { planName } = userWithSub.subscription;

    // Check limits using dynamic configuration
    if (shouldEnforceLimit(planName)) {
        const currentCount = userWithSub.subscription.freeTierBusinessPlanCount || 0;
        const limit = await planConfigService.getLimit(planName, 'businessPlan');

        if (currentCount >= limit) {
            return res.status(403).json({
                error: `You have reached your limit of ${limit} plans for the ${planName} plan. Please upgrade for unlimited access.`
            });
        }
    }

    // 3. Generate the report
    try {
        console.log(`[CONTROLLER] Generating new report for planType: "${planType}" for user: ${user.email}`);
        
        const marketQuery = planType === 'ai' ? industry : idea;
        const marketData = await fetchMarketData(marketQuery);
        
        const finalPrompt = buildFullBusinessPlanPrompt(formData, marketData);
        
        const fullReportText = await generateContent(finalPrompt);

        // 4. Increment count and save user (only on success)
        if (planName === FREE_TIER_PLAN_NAME_BACKEND) {
            userWithSub.subscription.freeTierBusinessPlanCount = (userWithSub.subscription.freeTierBusinessPlanCount || 0) + 1;
        } else if (planName === PRO_PLAN_NAME_BACKEND) {
            userWithSub.subscription.proTierBusinessPlanCount = (userWithSub.subscription.proTierBusinessPlanCount || 0) + 1;
        }

        await userWithSub.save();
        console.log(`[CONTROLLER] Successfully incremented plan count for user: ${userWithSub.email}`);

        // 5. Send response with report and updated subscription
        res.status(200).json({
            fullBusinessReport: fullReportText,
            subscription: userWithSub.subscription
        });

    } catch (error) {
        console.error(`Controller Error during report generation for user ${user.email}:`, error.message);
        res.status(500).json({
            error: error.message || 'An internal server error occurred while generating the report.'
        });
    }
};