import React, { useState, useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
    FiArrowRight, FiLoader, FiCheckCircle, FiCircle,
    FiAlertTriangle, FiHelpCircle, FiInfo, FiGlobe
} from 'react-icons/fi';
import { updateUserSubscription, selectAuthToken } from '../../../../../../store/features/auth/authSlice';

// Import the component that knows how to parse and display the tagged text
import FormattedQADisplay from '../textFormat/FormattedQADisplay';

//==================================================================
//  1. Enhanced Form Input Components with Tooltips
//==================================================================

const Tooltip = ({ children, content }) => (
    <div className="group relative inline-block">
        {children}
        <div className="invisible group-hover:visible absolute z-10 w-64 p-3 mt-2 text-sm text-white bg-slate-800 border border-slate-600 rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-200 left-0">
            {content}
        </div>
    </div>
);

const FormInput = ({ id, label, placeholder, value, onChange, helpText, maxLength, tooltip, examples }) => (
    <div className="space-y-2">
        <div className="flex items-center gap-2">
            <label htmlFor={id} className="block text-sm font-medium text-slate-300">
                {label}
            </label>
            {tooltip && (
                <Tooltip content={tooltip}>
                    <FiInfo className="w-4 h-4 text-slate-400 hover:text-blue-400 cursor-help" />
                </Tooltip>
            )}
        </div>
        <input
            type="text"
            id={id}
            name={id}
            placeholder={placeholder}
            value={value}
            onChange={onChange}
            maxLength={maxLength}
            className="w-full px-4 py-3 bg-slate-900/50 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 hover:border-slate-500"
        />
        {helpText && (
            <div className="flex items-start gap-2">
                <FiInfo className="w-3 h-3 text-blue-400 mt-0.5 flex-shrink-0" />
                <p className="text-xs text-slate-400">{helpText}</p>
            </div>
        )}
        {examples && (
            <div className="text-xs text-slate-500">
                <span className="font-medium">Examples:</span> {examples}
            </div>
        )}
        {maxLength && (
            <p className="text-xs text-slate-400 text-right">
                {value.length}/{maxLength}
            </p>
        )}
    </div>
);

const FormTextarea = ({ id, label, placeholder, value, onChange, helpText, maxLength, rows = 4, tooltip, examples }) => (
    <div className="space-y-2">
        <div className="flex items-center gap-2">
            <label htmlFor={id} className="block text-sm font-medium text-slate-300">
                {label}
            </label>
            {tooltip && (
                <Tooltip content={tooltip}>
                    <FiInfo className="w-4 h-4 text-slate-400 hover:text-blue-400 cursor-help" />
                </Tooltip>
            )}
        </div>
        <textarea
            id={id}
            name={id}
            placeholder={placeholder}
            value={value}
            onChange={onChange}
            maxLength={maxLength}
            rows={rows}
            className="w-full px-4 py-3 bg-slate-900/50 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-vertical hover:border-slate-500"
        />
        {helpText && (
            <div className="flex items-start gap-2">
                <FiInfo className="w-3 h-3 text-blue-400 mt-0.5 flex-shrink-0" />
                <p className="text-xs text-slate-400">{helpText}</p>
            </div>
        )}
        {examples && (
            <div className="text-xs text-slate-500">
                <span className="font-medium">Examples:</span> {examples}
            </div>
        )}
        {maxLength && (
            <p className="text-xs text-slate-400 text-right">
                {value.length}/{maxLength}
            </p>
        )}
    </div>
);

const FormSelect = ({ id, label, value, onChange, children, helpText, tooltip }) => (
    <div className="space-y-2">
        <div className="flex items-center gap-2">
            <label htmlFor={id} className="block text-sm font-medium text-slate-300">
                {label}
            </label>
            {tooltip && (
                <Tooltip content={tooltip}>
                    <FiInfo className="w-4 h-4 text-slate-400 hover:text-blue-400 cursor-help" />
                </Tooltip>
            )}
        </div>
        <select
            id={id}
            name={id}
            value={value}
            onChange={onChange}
            className="w-full px-4 py-3 bg-slate-900/50 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 hover:border-slate-500"
        >
            {children}
        </select>
        {helpText && (
            <div className="flex items-start gap-2">
                <FiInfo className="w-3 h-3 text-blue-400 mt-0.5 flex-shrink-0" />
                <p className="text-xs text-slate-400">{helpText}</p>
            </div>
        )}
    </div>
);

/**
 * A component to display animated steps while the AI is generating the answer.
 */
const GeneratingAnswerSteps = () => {
    const steps = [
        "Analyzing Your Question...",
        "Researching Best Practices...",
        "Formulating Strategic Insights...",
        "Finalizing Your Answer..."
    ];
    const [currentStep, setCurrentStep] = useState(0);

    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentStep(prev => (prev < steps.length - 1 ? prev + 1 : prev));
        }, 1800);
        return () => clearInterval(timer);
    }, [steps.length]);

    return (
        <div className="w-full bg-slate-900/50 border border-slate-700 rounded-2xl p-8 flex flex-col items-center text-center animate-fade-in">
            <h3 className="text-xl font-bold text-blue-400 mb-6">Your Answer is Being Generated...</h3>
            <div className="space-y-4 w-full max-w-sm">
                {steps.map((step, index) => {
                    const isCompleted = index < currentStep;
                    const isCurrent = index === currentStep;
                    return (
                        <div key={index} className="flex items-center text-left text-slate-300 transition-opacity duration-500" style={{ opacity: isCompleted || isCurrent ? 1 : 0.5 }}>
                            {isCompleted ? <FiCheckCircle className="w-5 h-5 text-green-400 mr-3 flex-shrink-0" /> : <FiCircle className="w-5 h-5 text-slate-500 mr-3 flex-shrink-0" />}
                            <span className={isCurrent ? 'text-blue-400 font-medium' : ''}>{step}</span>
                        </div>
                    );
                })}
            </div>
        </div>
    );
};


//==================================================================
//  2. The Main Form Component
//==================================================================
const BusinessQAForm = () => {
    const dispatch = useDispatch();
    const token = useSelector(selectAuthToken);
    
    // State for form fields
    const [formData, setFormData] = useState({
        question: '',
        businessContext: '',
        industry: '',
        businessStage: '',
        specificArea: '',
        language: 'English'
    });

    // State for managing API interaction (idle, loading, success, error)
    const [apiState, setApiState] = useState('idle');
    const [error, setError] = useState(null);
    const [generatedAnswer, setGeneratedAnswer] = useState('');

    // Memoized change handler for form inputs to prevent unnecessary re-renders
    const handleChange = useCallback((e) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
    }, []);

    // Function to handle form submission
    const handleSubmit = async (e) => {
        e.preventDefault();
        setError(null);
        setApiState('loading');

        try {
            const response = await fetch('/api/business-qa/generate', {
                method: 'POST',
                headers: { 
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify(formData),
            });
            const data = await response.json();
            if (!response.ok) {
                throw new Error(data.error || `HTTP error! status: ${response.status}`);
            }
            
            // Update user subscription in Redux if returned from backend
            if (data.subscription) {
                dispatch(updateUserSubscription(data.subscription));
            }
            
            setGeneratedAnswer(data.generatedAnswer);
            setApiState('success');
        } catch (err) {
            setError(err.message);
            setApiState('error');
            console.error('API Error:', err);
        }
    };

    // Function to reset the form and state to start over
    const handleReset = () => {
        setApiState('idle');
        setGeneratedAnswer('');
        setError(null);
        // Optionally reset form data:
        // setFormData({ question: '', businessContext: '', industry: '', businessStage: '', specificArea: '' });
    };

    // Conditional Rendering: Show loading animation
    if (apiState === 'loading') {
        return <GeneratingAnswerSteps />;
    }
    
    // Conditional Rendering: Show the formatted result
    if (apiState === 'success') {
        return <FormattedQADisplay answerText={generatedAnswer} onReset={handleReset} language={formData.language} />;
    }

    // Default Render: Show the form for 'idle' or 'error' states
    return (
        <form onSubmit={handleSubmit} className="w-full space-y-8 animate-fade-in">
            {/* Language Selection */}
            <div className="bg-blue-500/10 border border-blue-500/30 rounded-xl p-4">
                <FormSelect
                    id="language"
                    label="Response Language"
                    value={formData.language}
                    onChange={handleChange}
                    helpText="Choose the language for your business answer. The AI will respond in your selected language."
                    tooltip="The AI consultant can provide answers in multiple languages. Select your preferred language for the most comfortable reading experience."
                >
                    <option value="English">🇺🇸 English</option>
                    <option value="Spanish">🇪🇸 Español</option>
                    <option value="French">🇫🇷 Français</option>
                    <option value="German">🇩🇪 Deutsch</option>
                    <option value="Italian">🇮🇹 Italiano</option>
                    <option value="Portuguese">🇵🇹 Português</option>
                    <option value="Arabic">🇸🇦 العربية</option>
                    <option value="Hebrew">🇮🇱 עברית</option>
                    <option value="Chinese">🇨🇳 中文</option>
                    <option value="Japanese">🇯🇵 日本語</option>
                    <option value="Korean">🇰🇷 한국어</option>
                    <option value="Russian">🇷🇺 Русский</option>
                </FormSelect>
            </div>

            {/* Main Question */}
            <FormTextarea
                id="question"
                label="Your Business Question"
                placeholder="e.g., How should I price my SaaS product for maximum growth?"
                value={formData.question}
                onChange={handleChange}
                helpText="Ask any business-related question for expert advice and strategic insights. Be specific for more targeted advice."
                tooltip="This is the core question you want answered. The more specific and detailed your question, the more targeted and useful the AI's response will be. Include relevant details about your situation."
                examples="'How do I scale my team from 10 to 50 employees?', 'What pricing strategy works best for B2B SaaS?', 'How should I approach international expansion?'"
                maxLength={1000}
                rows={4}
            />

            <div className="grid md:grid-cols-2 gap-6">
                <FormInput
                    id="businessContext"
                    label="Business Context (Optional)"
                    placeholder="e.g., B2B SaaS startup, 50 employees, $2M ARR"
                    value={formData.businessContext}
                    onChange={handleChange}
                    helpText="Provide context about your business size, type, and current situation for more tailored advice."
                    tooltip="This helps the AI understand your business's current state and provide more relevant recommendations. Include details like company size, revenue, business model, or current challenges."
                    examples="'Early-stage startup, 5 employees', 'Established company, 200+ employees, $10M revenue', 'Solo entrepreneur, service-based business'"
                    maxLength={200}
                />
                <FormSelect
                    id="industry"
                    label="Industry (Optional)"
                    value={formData.industry}
                    onChange={handleChange}
                    helpText="Select your industry to receive insights specific to your market and sector."
                    tooltip="Industry-specific advice considers unique challenges, regulations, market dynamics, and best practices relevant to your sector."
                >
                    <option value="">Select Industry</option>
                    <option value="Technology">Technology & Software</option>
                    <option value="Healthcare">Healthcare & Medical</option>
                    <option value="Finance">Finance & Banking</option>
                    <option value="E-commerce">E-commerce & Retail</option>
                    <option value="Manufacturing">Manufacturing</option>
                    <option value="Consulting">Consulting & Services</option>
                    <option value="Education">Education & Training</option>
                    <option value="Real Estate">Real Estate</option>
                    <option value="Food & Beverage">Food & Beverage</option>
                    <option value="Media">Media & Entertainment</option>
                    <option value="Transportation">Transportation & Logistics</option>
                    <option value="Energy">Energy & Utilities</option>
                    <option value="Other">Other</option>
                </FormSelect>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
                <FormSelect
                    id="businessStage"
                    label="Business Stage (Optional)"
                    value={formData.businessStage}
                    onChange={handleChange}
                    helpText="Your current business maturity level helps tailor advice to your specific challenges and opportunities."
                    tooltip="Different business stages have unique challenges and priorities. Early-stage businesses focus on validation and growth, while established companies focus on optimization and scaling."
                >
                    <option value="">Select Stage</option>
                    <option value="Idea">💡 Idea Stage (Concept development)</option>
                    <option value="Startup">🚀 Startup (0-2 years, early traction)</option>
                    <option value="Growth">📈 Growth Stage (2-5 years, scaling)</option>
                    <option value="Established">🏢 Established (5+ years, mature)</option>
                </FormSelect>
                <FormSelect
                    id="specificArea"
                    label="Focus Area (Optional)"
                    value={formData.specificArea}
                    onChange={handleChange}
                    helpText="Select the business domain most relevant to your question for specialized insights."
                    tooltip="Choosing a focus area helps the AI provide more targeted advice with specific frameworks, metrics, and best practices relevant to that business function."
                >
                    <option value="">Select Focus Area</option>
                    <option value="Strategy">🎯 Strategy & Planning</option>
                    <option value="Marketing">📢 Marketing & Sales</option>
                    <option value="Operations">⚙️ Operations & Processes</option>
                    <option value="Finance">💰 Finance & Funding</option>
                    <option value="HR">👥 Human Resources & Team</option>
                    <option value="Technology">💻 Technology & Innovation</option>
                    <option value="Legal">⚖️ Legal & Compliance</option>
                    <option value="Growth">🚀 Growth & Scaling</option>
                    <option value="Leadership">👑 Leadership & Management</option>
                    <option value="Customer">🤝 Customer Experience</option>
                </FormSelect>
            </div>

            {/* Enhanced error message display */}
            {apiState === 'error' && error && (
                <div className="bg-gradient-to-r from-red-500/10 to-red-600/10 border border-red-500/30 text-red-300 p-6 rounded-xl flex items-start text-sm shadow-lg">
                    <div className="bg-red-500/20 p-2 rounded-lg mr-4 flex-shrink-0">
                        <FiAlertTriangle className="w-5 h-5" />
                    </div>
                    <div>
                        <div className="font-semibold text-red-400 mb-1">Generation Failed</div>
                        <div className="text-red-300">{error}</div>
                        <div className="text-red-400/80 text-xs mt-2">
                            Please check your question and try again. If the problem persists, try rephrasing your question or contact support.
                        </div>
                    </div>
                </div>
            )}

            {/* Enhanced submit button */}
            <div className="pt-6 flex justify-center">
                <button
                    type="submit"
                    disabled={!formData.question.trim()}
                    className="group flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:from-slate-600 disabled:to-slate-700 disabled:cursor-not-allowed text-white font-bold rounded-xl shadow-xl transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100 min-w-[200px]"
                >
                    <FiHelpCircle className="mr-3 w-5 h-5" />
                    Get Expert Answer
                    <FiArrowRight className="ml-3 w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
                </button>
            </div>

            {/* Form tips */}
            <div className="bg-slate-800/50 border border-slate-600/50 rounded-xl p-4 text-center">
                <div className="flex items-center justify-center mb-2">
                    <FiInfo className="w-4 h-4 text-blue-400 mr-2" />
                    <span className="text-sm font-medium text-slate-300">Pro Tips</span>
                </div>
                <p className="text-xs text-slate-400 leading-relaxed">
                    For the best results, be specific about your situation and include relevant context.
                    The AI can help with strategy, operations, marketing, finance, and more business areas.
                </p>
            </div>
        </form>
    );
};

export default BusinessQAForm;
