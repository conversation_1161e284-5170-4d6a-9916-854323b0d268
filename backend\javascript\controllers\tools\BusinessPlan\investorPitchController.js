// src/controllers/Tools/Business/investorPitchController.js
import User from '../../../models/User.js';
import {
    ensureSubscription,
    FREE_TIER_PLAN_NAME_BACKEND,
    PRO_PLAN_NAME_BACKEND,
    shouldEnforceLimit,
} from '../../common/user/limit/planConfig.js';
import planConfigService from '../../../services/planConfigService.js';
import axios from 'axios';

// Python service URL for investor pitch generation
const PYTHON_INVESTOR_PITCH_URL = process.env.PYTHON_PDF_PROCESSOR_URL || 'http://localhost:5001';

/**
 * Call Python service to generate investor pitch with advanced document processing
 * @param {Object} formData - Form data from frontend
 * @param {Object} file - Multer file object (optional)
 * @returns {Promise<string>} - Generated pitch content
 */
const callPythonInvestorPitchService = async (formData, file = null) => {
    try {
        // Use axios with FormData for better compatibility
        const FormData = (await import('form-data')).default;
        const pythonFormData = new FormData();

        // Add all form fields to FormData
        Object.keys(formData).forEach(key => {
            if (formData[key] !== undefined && formData[key] !== null) {
                pythonFormData.append(key, formData[key]);
            }
        });

        // Add file if provided with enhanced error handling
        if (file) {
            console.log(`[PITCH_CONTROLLER] Processing file upload:`, {
                originalname: file.originalname,
                mimetype: file.mimetype,
                size: file.size,
                hasBuffer: !!file.buffer,
                bufferLength: file.buffer ? file.buffer.length : 0
            });

            if (!file.buffer) {
                throw new Error('File buffer is not available. Ensure multer is configured with memoryStorage.');
            }

            pythonFormData.append('businessPlanFile', file.buffer, {
                filename: file.originalname,
                contentType: file.mimetype || 'application/pdf'
            });
        }

        console.log(`[PITCH_CONTROLLER] Calling Python service at: ${PYTHON_INVESTOR_PITCH_URL}/api/investor-pitch/generate`);
        console.log(`[PITCH_CONTROLLER] Form data keys:`, Object.keys(formData));
        console.log(`[PITCH_CONTROLLER] File info:`, file ? `${file.originalname} (${file.size} bytes)` : 'No file');

        // Test Python service connectivity first
        try {
            const healthCheck = await axios.get(`${PYTHON_INVESTOR_PITCH_URL}/api/investor-pitch/health`, { timeout: 5000 });
            console.log(`[PITCH_CONTROLLER] Python service health check passed:`, healthCheck.data);
        } catch (healthError) {
            console.error(`[PITCH_CONTROLLER] Python service health check failed:`, healthError.message);
            throw new Error(`Python service is not available at ${PYTHON_INVESTOR_PITCH_URL}. Please ensure the Python backend is running.`);
        }

        const response = await axios.post(`${PYTHON_INVESTOR_PITCH_URL}/api/investor-pitch/generate`, pythonFormData, {
            headers: {
                ...pythonFormData.getHeaders(),
            },
            timeout: 120000, // 2 minutes timeout for AI generation
            maxBodyLength: Infinity,
            maxContentLength: Infinity
        });

        const responseData = response.data;

        if (!responseData.generatedPitch) {
            throw new Error('Python service returned empty pitch content');
        }

        console.log(`[PITCH_CONTROLLER] Python service generated pitch successfully`);
        return responseData.generatedPitch;

    } catch (error) {
        console.error('Error calling Python investor pitch service:', error);
        if (error.response) {
            console.error('Python service error response:', error.response.data);
            throw new Error(error.response.data.error || `Python service error: ${error.response.status}`);
        }
        throw new Error(`Failed to generate pitch: ${error.message}`);
    }
};

export const generateInvestorPitch = async (req, res) => {
    console.log(`[PITCH_CONTROLLER] Received request body:`, req.body);
    console.log(`[PITCH_CONTROLLER] File uploaded:`, req.file ? req.file.originalname : 'No file');

    // Extract form data
    const {
        projectName,
        industry,
        projectDescription,
        problemStatement,
        solution,
        targetAudience,
        pitchObjective,
        language = 'English'
    } = req.body;

    // Validation logic based on file upload
    if (req.file) {
        // PDF uploaded - no other fields are required, just optional project name
        console.log(`[PITCH_CONTROLLER] PDF uploaded: ${req.file.originalname}, skipping field validation`);
    } else {
        // No PDF uploaded - all key fields are required
        const requiredFields = [
            { field: projectName, name: 'projectName', label: 'Project Name' },
            { field: industry, name: 'industry', label: 'Industry' },
            { field: projectDescription, name: 'projectDescription', label: 'Project Description' },
            { field: problemStatement, name: 'problemStatement', label: 'Problem Statement' },
            { field: solution, name: 'solution', label: 'Solution' },
            { field: targetAudience, name: 'targetAudience', label: 'Target Audience' },
            { field: pitchObjective, name: 'pitchObjective', label: 'Pitch Objective' }
        ];

        const missingFields = requiredFields.filter(({ field }) => !field || !field.trim());

        if (missingFields.length > 0) {
            const missingFieldNames = missingFields.map(({ label }) => label).join(', ');
            return res.status(400).json({
                error: `Missing required fields: ${missingFieldNames}. Please fill in all required fields or upload a business plan document.`
            });
        }

        console.log(`[PITCH_CONTROLLER] No PDF uploaded, all required fields validated successfully`);
    }

    try {
        console.log(`[PITCH_CONTROLLER] Generating investor pitch for project: "${req.body.projectName}"`);

        let user = await User.findById(req.user.id);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        user = ensureSubscription(user);
        const { planName } = user.subscription;

        // Check limits using dynamic configuration
        if (shouldEnforceLimit(planName)) {
            const currentCount = user.subscription.freeTierInvestorPitchCount || 0;
            const limit = await planConfigService.getLimit(planName, 'investorPitch');

            if (currentCount >= limit) {
                return res.status(403).json({
                    error: `${planName} plan investor pitch limit reached (${limit}). Please upgrade for unlimited access.`
                });
            }
        }

        // Call Python service for advanced document processing and pitch generation
        const generatedPitch = await callPythonInvestorPitchService(req.body, req.file);

        // Increment usage count directly in the database (like other working controllers)
        if (planName === FREE_TIER_PLAN_NAME_BACKEND) {
            user.subscription.freeTierInvestorPitchCount = (user.subscription.freeTierInvestorPitchCount || 0) + 1;
        } else if (planName === PRO_PLAN_NAME_BACKEND) {
            user.subscription.proTierInvestorPitchCount = (user.subscription.proTierInvestorPitchCount || 0) + 1;
        }

        await user.save();
        console.log(`[PITCH_CONTROLLER] Successfully incremented pitch count for user: ${user.email}`);

        res.status(200).json({
            generatedPitch,
            subscription: user.subscription
        });

    } catch (error) {
        console.error(`[PITCH_CONTROLLER] Error during pitch generation:`, error.message);
        res.status(500).json({
            error: 'An internal server error occurred while generating the investor pitch.'
        });
    }
};
