// src/controllers/Tools/Business/businessQAController.js

import { generateContent } from '../../../services/geminiBusinessService.js';
import { buildBusinessQAPrompt } from './businessQAPrompts.js';
import User from '../../../models/User.js';
import {
    ensureSubscription,
    FREE_TIER_PLAN_NAME_BACKEND,
    PRO_PLAN_NAME_BACKEND,
    shouldEnforceLimit,
} from '../../common/user/limit/planConfig.js';
import planConfigService from '../../../services/planConfigService.js';

/**
 * Controller to handle business Q&A generation.
 */
export const generateBusinessQA = async (req, res) => {
    // 1. Extract and validate form data from the request body
    const {
        question,
    } = req.body;

    if (!question || question.trim().length === 0) {
        return res.status(400).json({
            error: 'Question is required. Please provide a business question.'
        });
    }

    if (question.length > 1000) {
        return res.status(400).json({
            error: 'Question is too long. Please limit your question to 1000 characters.'
        });
    }

    try {
        console.log(`[BUSINESS_QA_CONTROLLER] Generating answer for question: "${question.substring(0, 50)}..."`);

        // 2. Check user limits before generating
        let user = await User.findById(req.user.id);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        user = ensureSubscription(user);
        const { planName } = user.subscription;

        // Check limits based on plan using dynamic configuration
        if (shouldEnforceLimit(planName)) {
            const currentCount = user.subscription.freeTierBusinessQACount || 0;
            const limit = await planConfigService.getLimit(planName, 'businessQA');

            if (currentCount >= limit) {
                return res.status(403).json({
                    error: `${planName} plan business Q&A limit reached (${limit}). Please upgrade for unlimited access.`
                });
            }
        }

        // 3. Build the advanced prompt using the dedicated prompt-builder function
        const qaPrompt = buildBusinessQAPrompt(req.body);

        // 4. Call the generic Gemini service to generate the content
        const generatedAnswer = await generateContent(qaPrompt);

        // 5. Increment the usage count after successful generation
        if (planName === FREE_TIER_PLAN_NAME_BACKEND) {
            user.subscription.freeTierBusinessQACount = (user.subscription.freeTierBusinessQACount || 0) + 1;
        } else if (planName === PRO_PLAN_NAME_BACKEND) {
            user.subscription.proTierBusinessQACount = (user.subscription.proTierBusinessQACount || 0) + 1;
        }
        await user.save();

        // 6. Send the successful response back to the client
        res.status(200).json({
            generatedAnswer,
            subscription: user.subscription // Include updated subscription for instant UI update
        });

    } catch (error) {
        console.error(`[BUSINESS_QA_CONTROLLER] Error during Q&A generation:`, error.message);
        res.status(500).json({
            error: 'An internal server error occurred while generating the business answer.'
        });
    }
};