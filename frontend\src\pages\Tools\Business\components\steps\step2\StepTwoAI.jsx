import React, { useState, useEffect } from 'react';
import { FaDollarSign, FaIndustry, FaHashtag, FaWandMagicSparkles } from 'react-icons/fa6';

const FormField = ({ icon, label, children, delay }) => (
  <div 
    className="group"
    style={{ animationDelay: `${delay}ms` }}
  >
    <label className="flex items-center text-sm font-semibold text-slate-300 mb-3 group-hover:text-white transition-colors duration-300">
      <div className="p-2 bg-slate-800 rounded-lg mr-3 group-hover:bg-slate-700 transition-colors duration-300">
        {icon}
      </div>
      {label}
    </label>
    {children}
  </div>
);

const StepTwoAI = ({ onDataChange, initialData }) => {
  const [formData, setFormData] = useState({
    budget: initialData?.budget || '',
    industry: initialData?.industry || '',
    keywords: initialData?.keywords || '',
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  useEffect(() => {
    onDataChange(formData);
  }, [formData, onDataChange]);

  const inputClassName = "w-full px-4 py-3 rounded-xl bg-slate-900/50 border border-slate-700 hover:border-slate-600 focus:ring-2 focus:ring-sky-500 focus:border-sky-500 outline-none transition-all duration-300 text-white placeholder-slate-500 backdrop-blur-sm";
  const selectClassName = "w-full px-4 py-3 rounded-xl bg-slate-900/50 border border-slate-700 hover:border-slate-600 focus:ring-2 focus:ring-sky-500 focus:border-sky-500 outline-none transition-all duration-300 text-white backdrop-blur-sm";

  return (
    <div className="w-full max-w-2xl mx-4 px-4">
      <div className="text-center mb-10">
        <div className="inline-flex items-center justify-center p-3 bg-gradient-to-r from-purple-500/20 to-sky-500/20 rounded-full mb-6 backdrop-blur-sm border border-white/10">
          <FaWandMagicSparkles className="w-6 h-6 text-purple-400 mr-2" />
          <span className="text-purple-400 font-semibold">AI Preferences</span>
        </div>
        <h2 className="text-3xl font-bold bg-gradient-to-r from-white via-purple-200 to-sky-200 bg-clip-text text-transparent mb-3">
          Tell Us Your Vision
        </h2>
        <p className="text-slate-400 leading-relaxed">
          Help our AI understand your goals to generate the perfect business concept
        </p>
      </div>

      <div className="bg-slate-900/30 backdrop-blur-sm rounded-2xl border border-slate-700/50 p-8 space-y-8">
        <FormField
          icon={<FaDollarSign className="w-4 h-4 text-green-400" />}
          label="Investment Budget"
          delay={100}
        >
          <select
            name="budget"
            id="budget"
            value={formData.budget}
            onChange={handleChange}
            className={selectClassName}
          >
            <option value="" className="bg-slate-800">Select your budget range</option>
            <option value="< $1,000" className="bg-slate-800">🌱 Starter - Under $1,000</option>
            <option value="$1,000 - $5,000" className="bg-slate-800">🚀 Growth - $1,000 - $5,000</option>
            <option value="$5,000 - $25,000" className="bg-slate-800">💼 Professional - $5,000 - $25,000</option>
            <option value="> $25,000" className="bg-slate-800">💰 Premium - Over $25,000</option>
          </select>
        </FormField>

        <FormField
          icon={<FaIndustry className="w-4 h-4 text-blue-400" />}
          label="Industry Interest"
          delay={200}
        >
          <input
            type="text"
            name="industry"
            id="industry"
            value={formData.industry}
            onChange={handleChange}
            placeholder="Technology, E-commerce, Health & Wellness, Education..."
            className={inputClassName}
          />
        </FormField>

        <FormField
          icon={<FaHashtag className="w-4 h-4 text-purple-400" />}
          label="Keywords & Themes"
          delay={300}
        >
          <input
            type="text"
            name="keywords"
            id="keywords"
            value={formData.keywords}
            onChange={handleChange}
            placeholder="sustainable, mobile-first, for students, AI-powered..."
            className={inputClassName}
          />
          <p className="text-xs text-slate-500 mt-2 ml-1">
            Optional: Add specific themes or characteristics you'd like to explore
          </p>
        </FormField>
      </div>

      <div className="mt-8 text-center">
        <div className="inline-flex items-center space-x-2 text-slate-500 text-sm">
          <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse" />
          <span>AI analyzing preferences</span>
          <div className="w-2 h-2 bg-sky-500 rounded-full animate-pulse" />
        </div>
      </div>
    </div>
  );
};

export default StepTwoAI;