// hooks/usePlanConfiguration.js
import { useState, useEffect, useCallback } from 'react';

const API_BASE_URL = import.meta.env.VITE_NODE_BACKEND_URL || 'http://localhost:3001';

const usePlanConfiguration = () => {
  const [planConfig, setPlanConfig] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastFetch, setLastFetch] = useState(null);

  // Cache duration: 5 minutes
  const CACHE_DURATION = 5 * 60 * 1000;

  // Check if configuration is stale
  const isStale = useCallback(() => {
    if (!lastFetch) return true;
    return (Date.now() - lastFetch) > CACHE_DURATION;
  }, [lastFetch, CACHE_DURATION]);

  const fetchPlanConfiguration = useCallback(async (forceRefresh = false) => {
    // Check if we need to fetch (no cache or cache expired or force refresh)
    const now = Date.now();
    if (!forceRefresh && lastFetch && (now - lastFetch) < CACHE_DURATION && planConfig.length > 0) {
      return; // Use cached data
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_BASE_URL}/api/admin/public/plan-config`);

      if (!response.ok) {
        throw new Error('Failed to fetch plan configuration');
      }

      const data = await response.json();

      if (data.success && data.planConfig) {
        setPlanConfig(data.planConfig);
        setLastFetch(now);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      console.error('Error fetching plan configuration:', err);
      setError(err.message);

      // Fallback to static values if fetch fails
      setPlanConfig([
        {
          planName: 'Starter',
          displayName: 'Starter Plan',
          limits: {
            pdfUpload: 5,
            businessPlan: 1,
            investorPitch: 3,
            businessQA: 5,
            message: 100
          }
        },
        {
          planName: 'Pro',
          displayName: 'Pro Plan',
          limits: {
            pdfUpload: 25,
            businessPlan: 20,
            investorPitch: 25,
            businessQA: 50,
            message: 300
          }
        }
      ]);
    } finally {
      setLoading(false);
    }
  }, [lastFetch, planConfig.length, CACHE_DURATION]);

  // Initial fetch
  useEffect(() => {
    fetchPlanConfiguration();
  }, [fetchPlanConfiguration]);

  // Set up periodic refresh to catch admin changes
  useEffect(() => {
    const interval = setInterval(() => {
      // Only refresh if we're not currently loading and cache is stale
      if (!loading && isStale()) {
        fetchPlanConfiguration();
      }
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [loading, fetchPlanConfiguration, isStale]);

  // Get limits for a specific plan
  const getPlanLimits = useCallback((planName) => {
    const plan = planConfig.find(p => p.planName === planName);
    return plan ? plan.limits : null;
  }, [planConfig]);

  // Get specific limit for a plan and feature
  const getLimit = useCallback((planName, feature) => {
    const limits = getPlanLimits(planName);
    return limits ? limits[feature] || 0 : 0;
  }, [getPlanLimits]);

  // Refresh configuration (useful for real-time updates)
  const refreshConfiguration = useCallback(() => {
    return fetchPlanConfiguration(true);
  }, [fetchPlanConfiguration]);

  return {
    planConfig,
    loading,
    error,
    getPlanLimits,
    getLimit,
    refreshConfiguration,
    isStale,
    lastFetch
  };
};

export default usePlanConfiguration;
