// routes/admin/adminRoutes.js
import express from 'express';
import { protect } from '../../middleware/authMiddleware.js';
import { requireAdmin } from '../../middleware/adminMiddleware.js';
import {
    validateUserUpdate,
    validateUserId,
    validateUserQuery,
    validateSystemSettings
} from '../../middleware/validation.js';
import { asyncHandler } from '../../middleware/errorHandler.js';
import {
    getAllUsers,
    getUserById,
    updateUser,
    deleteUser,
    getAdminUsers,
    getSystemAnalytics,
    updateSystemSettings,
    checkAdminRegistrationStatus,
    getPlanConfiguration,
    updatePlanConfiguration,
    getUserPlanConfiguration,
    getPublicSubscriptionPlans,
    getSubscriptionPlans,
    getSubscriptionPlan,
    updateSubscriptionPlan,
    resetSubscriptionPlan,
    getSubscriptionStats,
    getCacheStats,
    clearCache,
    removeEnterprisePlans
} from '../../controllers/admin/adminController.js';

const router = express.Router();

// Public routes (no auth required)
router.get('/registration-status', asyncHandler(checkAdminRegistrationStatus));
router.get('/public/plan-config', asyncHandler(getUserPlanConfiguration));
router.get('/public/subscription-plans', asyncHandler(getPublicSubscriptionPlans));

// Apply authentication and admin protection to protected routes
router.use(protect);
router.use(requireAdmin);

// User management routes
router.get('/users', validateUserQuery, asyncHandler(getAllUsers));
router.get('/users/:id', validateUserId, asyncHandler(getUserById));
router.put('/users/:id', validateUserUpdate, asyncHandler(updateUser));
router.delete('/users/:id', validateUserId, asyncHandler(deleteUser));

// Admin management routes
router.get('/admins', asyncHandler(getAdminUsers));

// System analytics routes
router.get('/analytics', asyncHandler(getSystemAnalytics));

// System settings routes
router.put('/settings', validateSystemSettings, asyncHandler(updateSystemSettings));

// Plan configuration routes (actual runtime limits)
router.get('/plan-config', asyncHandler(getPlanConfiguration));
router.put('/plan-config', asyncHandler(updatePlanConfiguration));

// Payment/Subscription management routes
router.get('/subscription-plans', asyncHandler(getSubscriptionPlans));
router.get('/subscription-plans/:planName', asyncHandler(getSubscriptionPlan));
router.put('/subscription-plans/:planName', asyncHandler(updateSubscriptionPlan));
router.post('/subscription-plans/:planName/reset', asyncHandler(resetSubscriptionPlan));
router.post('/subscription-plans/remove-enterprise', asyncHandler(removeEnterprisePlans));
router.get('/subscription-stats', asyncHandler(getSubscriptionStats));

// Cache management routes
router.get('/cache/stats', asyncHandler(getCacheStats));
router.post('/cache/clear', asyncHandler(clearCache));

export default router;
