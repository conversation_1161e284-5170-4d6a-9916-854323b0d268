// common/user/limit/planConfig.js
import planConfigService from '../../../../services/planConfigService.js';
// Plan Names (constants for backward compatibility)
export const FREE_TIER_PLAN_NAME_BACKEND = 'Starter';
export const PRO_PLAN_NAME_BACKEND = 'Pro';

// Legacy constants - these are now dynamic but kept for backward compatibility
// The actual values are fetched from the database via planConfigService
export let STARTER_BUSINESS_PLAN_LIMIT = 1;
export let STARTER_UPLOAD_LIMIT = 5;
export let STARTER_INVESTOR_PITCH_LIMIT = 3;
export let STARTER_BUSINESS_QA_LIMIT = 5;
export let STARTER_MESSAGE_LIMIT = 100;

export let PRO_UPLOAD_LIMIT = 25;
export let PRO_BUSINESS_PLAN_LIMIT = 20;
export let PRO_INVESTOR_PITCH_LIMIT = 25;
export let PRO_BUSINESS_QA_LIMIT = 50;
export let PRO_MESSAGE_LIMIT = 300;

// Helper function to check if a limit should be enforced for a plan
export const shouldEnforceLimit = (planName) => {
    return planConfigService.shouldEnforceLimit(planName);
};

// Dynamic helper function to get the appropriate limit for a plan and feature
export const getLimit = async (planName, feature) => {
    try {
        // Map feature names for backward compatibility
        const featureMap = {
            'businessPlan': 'businessPlan',
            'upload': 'pdfUpload',
            'investorPitch': 'investorPitch',
            'businessQA': 'businessQA',
            'message': 'message'
        };

        const mappedFeature = featureMap[feature] || feature;
        return await planConfigService.getLimit(planName, mappedFeature);
    } catch (error) {
        console.error(`Error getting limit for ${planName}.${feature}:`, error);
        return 0;
    }
};

// Synchronous version for backward compatibility (uses cached values)
export const getLimitSync = (planName, feature) => {
    // This is a fallback for synchronous calls
    // It uses the cached values from the service
    const cachedConfig = planConfigService.cache.get(planName);

    if (cachedConfig) {
        const featureMap = {
            'businessPlan': 'businessPlan',
            'upload': 'pdfUpload',
            'investorPitch': 'investorPitch',
            'businessQA': 'businessQA',
            'message': 'message'
        };

        const mappedFeature = featureMap[feature] || feature;
        return cachedConfig.limits[mappedFeature] || 0;
    }

    // Fallback to static values if cache is empty
    const staticLimits = {
        [FREE_TIER_PLAN_NAME_BACKEND]: {
            businessPlan: 1,
            upload: 5,
            investorPitch: 3,
            businessQA: 5,
            message: 100
        },
        [PRO_PLAN_NAME_BACKEND]: {
            businessPlan: 20,
            upload: 25,
            investorPitch: 25,
            businessQA: 50,
            message: 300
        }
    };

    return staticLimits[planName]?.[feature] || 0;
};

// Helper function to initialize subscription if it's missing
export const ensureSubscription = (user) => {
    if (!user.subscription) {
        user.subscription = {
            planName: FREE_TIER_PLAN_NAME_BACKEND,
            status: 'active',
            startDate: new Date(),
            freeTierUploadCount: 0,
            proTierUploadCount: 0,
            freeTierMessageCount: 0,
            proTierMessageCount: 0,
            freeTierBusinessPlanCount: 0,
            proTierBusinessPlanCount: 0,
            freeTierInvestorPitchCount: 0,
            proTierInvestorPitchCount: 0,
            freeTierBusinessQACount: 0,
            proTierBusinessQACount: 0,
            businessPlanMonthlyReset: new Date(),
            investorPitchMonthlyReset: new Date(),
            businessQADailyReset: new Date(),
        };
    }
    return user;
};